# -*- coding: utf-8 -*-
"""
资金流向视图模块

本模块包含与资金流向相关的视图函数：
1. 市场资金流向
2. 行业资金流向
3. 个股资金流向
4. 涨跌停数据
"""

from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.core.paginator import Paginator, PageNotAnInteger, EmptyPage
from django.utils import timezone
from django.views.generic import ListView
from datetime import datetime, timedelta
import logging

from .base_views import (
    StockBasic,
    StockLimitList,
    StockFundFlow,
    StockMarketFundFlow,
    StockSectorFundFlow,
)

logger = logging.getLogger(__name__)


def market_fund_flow(request):
    """市场资金流向视图

    展示市场整体资金流向数据，包括上证指数、深证成指的涨跌幅和主力资金流入情况

    Args:
        request: HTTP请求对象

    Returns:
        HttpResponse: 渲染后的市场资金流向页面
    """
    # 获取日期参数
    date_str = request.GET.get("date")
    if date_str:
        selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    else:
        # 默认使用最新交易日
        latest_data = StockMarketFundFlow.objects.order_by("-trade_date").first()
        selected_date = latest_data.trade_date if latest_data else timezone.now().date()

    # 获取最近30个交易日的市场资金流向数据
    end_date = selected_date
    start_date = end_date - timedelta(days=60)  # 获取更多数据以确保有30个交易日

    all_market_fund_flow_data = StockMarketFundFlow.objects.filter(
        trade_date__range=(start_date, end_date)
    ).order_by("-trade_date")

    # 分页处理
    page = request.GET.get("page", 1)
    rows_per_page = request.GET.get("rows_per_page", 10)  # 默认每页显示10条

    # 确保 rows_per_page 是有效的整数值
    try:
        rows_per_page = int(rows_per_page)
        if rows_per_page not in [5, 10, 20, 50, 100]:
            rows_per_page = 10
    except (ValueError, TypeError):
        rows_per_page = 10

    paginator = Paginator(all_market_fund_flow_data, rows_per_page)

    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    # 为了兼容现有代码，我们仍然保留 market_fund_flow_data
    market_fund_flow_data = page_obj.object_list

    # 计算主力资金累计净流入
    cumulative_inflow = round(
        sum(
            data.main_net_inflow
            for data in market_fund_flow_data
            if data.main_net_inflow is not None
        )
        / 100000000,
        2,
    )

    # 获取最新交易日期（用于日期选择器的最大值）
    latest_data = StockMarketFundFlow.objects.order_by("-trade_date").first()
    latest_date = latest_data.trade_date if latest_data else timezone.now().date()

    # 格式化净流入,流出单位为亿
    for item in market_fund_flow_data:
        item.main_net_inflow = round(item.main_net_inflow / 100000000, 2)
        item.super_big_net_inflow = round(item.super_big_net_inflow / 100000000, 2)
        item.big_net_inflow = round(item.big_net_inflow / 100000000, 2)
        item.medium_net_inflow = round(item.medium_net_inflow / 100000000, 2)
        item.small_net_inflow = round(item.small_net_inflow / 100000000, 2)

    context = {
        "market_fund_flow_data": market_fund_flow_data,
        "page_obj": page_obj,
        "selected_date": selected_date,
        "latest_date": latest_date,
        "cumulative_inflow": cumulative_inflow,
        "rows_per_page": rows_per_page,
    }

    return render(request, "market_data/market_fund_flow.html", context)


def market_fund_flow_trend(request):
    """市场资金流向趋势图视图

    展示市场资金流向的趋势图表和分析

    Args:
        request: HTTP请求对象

    Returns:
        HttpResponse: 渲染后的趋势图页面
    """
    return render(request, "market_data/market_fund_flow_trend.html")


def api_market_fund_flow_trend(request):
    """API: 获取市场资金流向趋势数据

    Args:
        request: HTTP请求对象

    Returns:
        JsonResponse: 趋势数据
    """
    from django.http import JsonResponse
    import logging

    logger = logging.getLogger(__name__)

    # 获取参数
    days = int(request.GET.get("days", 60))  # 获取天数，默认60天

    try:
        # 获取最近指定天数的市场资金流向数据
        from datetime import datetime, timedelta

        end_date = datetime.now().date()
        start_date = end_date - timedelta(
            days=days * 2
        )  # 获取更多数据以确保有足够的交易日

        fund_flow_data = StockMarketFundFlow.objects.filter(
            trade_date__range=(start_date, end_date)
        ).order_by("-trade_date")[:days]

        # 按时间正序排列
        fund_flow_data = list(reversed(fund_flow_data))

        logger.info(f"获取到 {len(fund_flow_data)} 条市场资金流向数据")

        # 转换为图表数据格式
        chart_data = []
        fund_type_summary = {"super_big": 0, "big": 0, "medium": 0, "small": 0}

        for item in fund_flow_data:
            # 主力资金流向数据
            main_net_inflow = (
                round(float(item.main_net_inflow) / 100000000, 2)
                if item.main_net_inflow
                else 0
            )
            super_big_net_inflow = (
                round(float(item.super_big_net_inflow) / 100000000, 2)
                if item.super_big_net_inflow
                else 0
            )
            big_net_inflow = (
                round(float(item.big_net_inflow) / 100000000, 2)
                if item.big_net_inflow
                else 0
            )
            medium_net_inflow = (
                round(float(item.medium_net_inflow) / 100000000, 2)
                if item.medium_net_inflow
                else 0
            )
            small_net_inflow = (
                round(float(item.small_net_inflow) / 100000000, 2)
                if item.small_net_inflow
                else 0
            )

            # 累计各类型资金流向
            fund_type_summary["super_big"] += super_big_net_inflow
            fund_type_summary["big"] += big_net_inflow
            fund_type_summary["medium"] += medium_net_inflow
            fund_type_summary["small"] += small_net_inflow

            chart_data.append(
                {
                    "trade_date": item.trade_date.strftime("%Y-%m-%d"),
                    "sh_index_close": (
                        float(item.sh_index_close) if item.sh_index_close else 0
                    ),
                    "sh_index_change_pct": (
                        float(item.sh_index_change_pct)
                        if item.sh_index_change_pct
                        else 0
                    ),
                    "sz_index_close": (
                        float(item.sz_index_close) if item.sz_index_close else 0
                    ),
                    "sz_index_change_pct": (
                        float(item.sz_index_change_pct)
                        if item.sz_index_change_pct
                        else 0
                    ),
                    "main_net_inflow": main_net_inflow,
                    "super_big_net_inflow": super_big_net_inflow,
                    "big_net_inflow": big_net_inflow,
                    "medium_net_inflow": medium_net_inflow,
                    "small_net_inflow": small_net_inflow,
                }
            )

        # 计算统计指标
        total_main_inflow = sum(item["main_net_inflow"] for item in chart_data)
        avg_main_inflow = (
            round(total_main_inflow / len(chart_data), 2) if chart_data else 0
        )

        # 计算上证指数涨跌幅统计
        sh_changes = [
            item["sh_index_change_pct"]
            for item in chart_data
            if item["sh_index_change_pct"] != 0
        ]
        sh_up_days = len([x for x in sh_changes if x > 0])
        sh_down_days = len([x for x in sh_changes if x < 0])

        # 计算深证指数涨跌幅统计
        sz_changes = [
            item["sz_index_change_pct"]
            for item in chart_data
            if item["sz_index_change_pct"] != 0
        ]
        sz_up_days = len([x for x in sz_changes if x > 0])
        sz_down_days = len([x for x in sz_changes if x < 0])

        # 返回完整的数据结构
        response_data = {
            "success": True,
            "data": chart_data,
            "summary": {
                "total_days": len(chart_data),
                "total_main_inflow": round(total_main_inflow, 2),
                "avg_main_inflow": avg_main_inflow,
                "fund_type_summary": {
                    "super_big": round(fund_type_summary["super_big"], 2),
                    "big": round(fund_type_summary["big"], 2),
                    "medium": round(fund_type_summary["medium"], 2),
                    "small": round(fund_type_summary["small"], 2),
                },
                "index_stats": {
                    "sh_up_days": sh_up_days,
                    "sh_down_days": sh_down_days,
                    "sh_up_rate": (
                        round(sh_up_days / len(sh_changes) * 100, 1)
                        if sh_changes
                        else 0
                    ),
                    "sz_up_days": sz_up_days,
                    "sz_down_days": sz_down_days,
                    "sz_up_rate": (
                        round(sz_up_days / len(sz_changes) * 100, 1)
                        if sz_changes
                        else 0
                    ),
                },
            },
        }

        logger.info(
            f"返回数据: {len(chart_data)} 条记录，主力净流入总计: {total_main_inflow}亿"
        )
        return JsonResponse(response_data)

    except Exception as e:
        logger.error(f"获取市场资金流向趋势数据失败: {str(e)}")
        return JsonResponse({"success": False, "error": str(e)})


def sector_fund_flow(request):
    """行业资金流向视图

    展示各行业板块的资金流向数据，包括行业涨跌幅和主力资金流入情况

    Args:
        request: HTTP请求对象

    Returns:
        HttpResponse: 渲染后的行业资金流向页面
    """
    # 获取日期参数
    date_str = request.GET.get("date")
    if date_str:
        selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    else:
        # 默认使用最新交易日
        latest_data = StockSectorFundFlow.objects.order_by("-trade_date").first()
        selected_date = latest_data.trade_date if latest_data else timezone.now().date()

    # 获取板块类型参数
    sector_type = request.GET.get("sector_type", "行业板块")

    # 获取行业资金流向数据
    sector_fund_flow_data = StockSectorFundFlow.objects.filter(
        trade_date=selected_date, sector_type=sector_type
    ).order_by("-net_inflow_amount")

    # 分页处理
    page = request.GET.get("page", 1)
    rows_per_page = request.GET.get("rows_per_page", 10)  # 默认每页显示10条

    # 确保 rows_per_page 是有效的整数值
    try:
        rows_per_page = int(rows_per_page)
        if rows_per_page not in [5, 10, 20, 50, 100]:
            rows_per_page = 10
    except (ValueError, TypeError):
        rows_per_page = 10

    paginator = Paginator(sector_fund_flow_data, rows_per_page)
    total_count = paginator.count  # 获取总条数

    try:
        sectors = paginator.page(page)
    except PageNotAnInteger:
        sectors = paginator.page(1)
    except EmptyPage:
        sectors = paginator.page(paginator.num_pages)

    # 获取最新交易日期（用于日期选择器的最大值）
    latest_data = StockSectorFundFlow.objects.order_by("-trade_date").first()
    latest_date = latest_data.trade_date if latest_data else timezone.now().date()

    # 获取可用的板块类型
    sector_types = StockSectorFundFlow.objects.values_list(
        "sector_type", flat=True
    ).distinct()

    # 格式化资金流入数据为亿
    for item in sectors:
        item.net_inflow_amount = round(item.net_inflow_amount / 100000000, 2)
        item.super_big_net_inflow = round(item.super_big_net_inflow / 100000000, 2)
        item.big_net_inflow = round(item.big_net_inflow / 100000000, 2)
        item.medium_net_inflow = round(item.medium_net_inflow / 100000000, 2)
        item.small_net_inflow = round(item.small_net_inflow / 100000000, 2)

    context = {
        "sectors": sectors,
        "selected_date": selected_date,
        "latest_date": latest_date,
        "sector_type": sector_type,
        "sector_types": sector_types,
        "total_count": total_count,
        "rows_per_page": rows_per_page,
    }

    return render(request, "market_data/sector_fund_flow.html", context)


def stock_fund_flow(request):
    """个股资金流向视图

    展示个股的资金流向数据，包括个股涨跌幅和主力资金流入情况

    Args:
        request: HTTP请求对象

    Returns:
        HttpResponse: 渲染后的个股资金流向页面
    """
    # 获取日期参数
    date_str = request.GET.get("date")
    if date_str:
        selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    else:
        # 默认使用最新交易日
        latest_data = (
            StockFundFlow.objects.filter(type="个股").order_by("-date").first()
        )
        selected_date = latest_data.date if latest_data else timezone.now().date()

    # 获取搜索参数
    search_query = request.GET.get("q", "")

    # 构建查询条件
    query = Q(date=selected_date, type="个股")
    if search_query:
        query &= Q(code__icontains=search_query) | Q(name__icontains=search_query)

    # 获取排序参数
    sort_by = request.GET.get("sort", "-main_net_inflow")

    # 获取个股资金流向数据
    stock_fund_flow_data = StockFundFlow.objects.filter(query).order_by(sort_by)

    # 分页处理
    page = request.GET.get("page", 1)
    rows_per_page = int(request.GET.get("rows_per_page", 20))
    paginator = Paginator(stock_fund_flow_data, rows_per_page)

    try:
        stocks = paginator.page(page)
    except PageNotAnInteger:
        stocks = paginator.page(1)
    except EmptyPage:
        stocks = paginator.page(paginator.num_pages)

    # 获取最新交易日期（用于日期选择器的最大值）
    latest_data = StockFundFlow.objects.filter(type="个股").order_by("-date").first()
    latest_date = latest_data.date if latest_data else timezone.now().date()

    context = {
        "stocks": stocks,
        "selected_date": selected_date,
        "latest_date": latest_date,
        "search_query": search_query,
        "sort_by": sort_by,
        "rows_per_page": rows_per_page,
    }

    return render(request, "market_data/stock_fund_flow.html", context)


def stock_fund_flow_detail(request, code):
    """个股资金流向详情视图

    展示特定个股的历史资金流向数据

    Args:
        request: HTTP请求对象
        code: 股票代码

    Returns:
        HttpResponse: 渲染后的个股资金流向详情页面
    """
    # 获取股票基本信息
    stock = get_object_or_404(StockBasic, stock_code=code)

    # 获取最近30个交易日的资金流向数据
    fund_flow_data = StockFundFlow.objects.filter(code=code, type="个股").order_by(
        "-date"
    )[:30]

    # 计算主力资金累计净流入
    cumulative_inflow = sum(
        data.main_net_inflow
        for data in fund_flow_data
        if data.main_net_inflow is not None
    )

    # 获取同行业股票的资金流向数据（最新交易日）
    if stock.industry:
        latest_date = (
            fund_flow_data.first().date if fund_flow_data else timezone.now().date()
        )
        industry_stocks = StockBasic.objects.filter(industry=stock.industry)
        industry_codes = [s.stock_code for s in industry_stocks]

        industry_fund_flow = StockFundFlow.objects.filter(
            code__in=industry_codes, type="个股", date=latest_date
        ).order_by("-main_net_inflow")[:10]
    else:
        industry_fund_flow = []

    context = {
        "stock": stock,
        "fund_flow_data": fund_flow_data,
        "cumulative_inflow": cumulative_inflow,
        "industry_fund_flow": industry_fund_flow,
    }

    return render(request, "market_data/stock_fund_flow_detail.html", context)


def limit_list(request):
    """涨跌停数据视图

    展示当日涨停和跌停的股票列表

    Args:
        request: HTTP请求对象

    Returns:
        HttpResponse: 渲染后的涨跌停数据页面
    """
    # 获取日期参数
    date_str = request.GET.get("date")
    if date_str:
        selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    else:
        # 默认使用最新交易日
        latest_data = StockLimitList.objects.order_by("-date").first()
        selected_date = latest_data.date if latest_data else timezone.now().date()

    # 获取涨停类型参数
    limit_type = request.GET.get("type", "涨停")

    # 获取行业参数
    industry = request.GET.get("industry", "")

    # 构建查询条件
    query = Q(date=selected_date, limit_type=limit_type)
    if industry:
        query &= Q(industry=industry)

    # 获取涨跌停数据
    limit_data = StockLimitList.objects.filter(query).order_by("-continuous_limit")

    # 分页处理
    page = request.GET.get("page", 1)
    per_page = request.GET.get("per_page", 20)  # 默认每页显示20条

    # 确保 per_page 是有效的整数值
    try:
        per_page = int(per_page)
        if per_page not in [5, 10, 20, 50, 100]:
            per_page = 20
    except (ValueError, TypeError):
        per_page = 20

    paginator = Paginator(limit_data, per_page)

    try:
        stocks = paginator.page(page)
    except PageNotAnInteger:
        stocks = paginator.page(1)
    except EmptyPage:
        stocks = paginator.page(paginator.num_pages)

    # 获取最新交易日期（用于日期选择器的最大值）
    latest_data = StockLimitList.objects.order_by("-date").first()
    latest_date = latest_data.date if latest_data else timezone.now().date()

    # 获取可用的行业列表
    industries = (
        StockLimitList.objects.filter(date=selected_date, limit_type=limit_type)
        .values_list("industry", flat=True)
        .distinct()
    )

    # 统计数据
    limit_up_count = StockLimitList.objects.filter(
        date=selected_date, limit_type="涨停"
    ).count()
    limit_down_count = StockLimitList.objects.filter(
        date=selected_date, limit_type="跌停"
    ).count()

    # 格式化封单资金和流通市值
    for stock in stocks:
        stock.fund_amount = stock.fund_amount / 100000000
        stock.circulation_market_value = stock.circulation_market_value / 100000000

    context = {
        "stocks": stocks,
        "selected_date": selected_date,
        "latest_date": latest_date,
        "limit_type": limit_type,
        "industry": industry,
        "industries": industries,
        "limit_up_count": limit_up_count,
        "limit_down_count": limit_down_count,
        "per_page": per_page,
    }

    return render(request, "market_data/limit_list.html", context)


class StockLimitListView(ListView):
    """涨跌停数据类视图

    展示当日涨停和跌停的股票列表
    """

    model = StockLimitList
    template_name = "market_data/limit_list_class.html"
    context_object_name = "stocks"
    paginate_by = 20

    def get_queryset(self):
        # 获取日期参数
        date_str = self.request.GET.get("date")
        if date_str:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        else:
            # 默认使用最新交易日
            latest_data = StockLimitList.objects.order_by("-date").first()
            selected_date = latest_data.date if latest_data else timezone.now().date()

        # 获取涨停类型参数
        limit_type = self.request.GET.get("type", "涨停")

        # 获取行业参数
        industry = self.request.GET.get("industry", "")

        # 构建查询条件
        query = Q(date=selected_date, limit_type=limit_type)
        if industry:
            query &= Q(industry=industry)

        # 返回查询结果
        return StockLimitList.objects.filter(query).order_by("-continuous_limit")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # 获取日期参数
        date_str = self.request.GET.get("date")
        if date_str:
            selected_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        else:
            # 默认使用最新交易日
            latest_data = StockLimitList.objects.order_by("-date").first()
            selected_date = latest_data.date if latest_data else timezone.now().date()

        # 获取涨停类型参数
        limit_type = self.request.GET.get("type", "涨停")

        # 获取行业参数
        industry = self.request.GET.get("industry", "")

        # 获取最新交易日期（用于日期选择器的最大值）
        latest_data = StockLimitList.objects.order_by("-date").first()
        latest_date = latest_data.date if latest_data else timezone.now().date()

        # 获取可用的行业列表
        industries = (
            StockLimitList.objects.filter(date=selected_date, limit_type=limit_type)
            .values_list("industry", flat=True)
            .distinct()
        )

        # 统计数据
        limit_up_count = StockLimitList.objects.filter(
            date=selected_date, limit_type="涨停"
        ).count()
        limit_down_count = StockLimitList.objects.filter(
            date=selected_date, limit_type="跌停"
        ).count()

        # 添加额外的上下文数据
        context.update(
            {
                "selected_date": selected_date,
                "latest_date": latest_date,
                "limit_type": limit_type,
                "industry": industry,
                "industries": industries,
                "limit_up_count": limit_up_count,
                "limit_down_count": limit_down_count,
            }
        )

        return context
