# 股票数据分析系统 (Stock Data Analysis System)

这是一个基于 Django 5.1.7 开发的股票数据分析系统，提供股票行情、财务数据分析、资金流向分析、活跃营业部和涨跌停数据等功能。系统采用 Tabler UI 框架设计，支持数据可视化展示，并具有智能数据更新机制。

## 主要功能

### 市场数据模块 (market_data)

- **股票基础信息**: 股票代码、名称、行业分类等基本信息
- **行业板块分析**: 行业板块排行、成分股分析、板块资金流向
- **概念板块分析**: 概念板块数据、热点概念追踪
- **资金流向分析**: 个股资金流向、板块资金流向、市场整体资金流向
- **涨跌停数据**: 涨停股票、跌停股票、一字板统计
- **活跃营业部**: 营业部交易排行、龙虎榜数据
- **市场指数**: 主要指数行情、指数成分股
- **IPO数据**: 新股发行、上市时间表
- **融资融券**: 融资融券余额、标的股票
- **港股通数据**: 沪深港通资金流向
- **风险警示**: ST股票、退市风险股票

### 财务分析模块 (financial_analysis)

- **财务指标分析**: 营收、净利润、ROE、ROA等核心财务指标
- **财务报表筛选**: 多维度财务数据筛选和排序
- **分红派息**: 股票分红历史、除权除息日期
- **股东信息**: 股东持股变化、机构持股统计
- **行业PE分析**: 行业市盈率对比、估值分析
- **技术指标**: 技术分析指标计算和展示
- **产业链分析**: 上下游产业链关系图谱

## 功能特点

### 市场数据

- 股票基本信息查询 - 支持多维度筛选和排序
- 实时行情数据展示 - 实时更新股票价格和涨跌幅
- 历史行情数据查询 - 支持K线图和技术指标
- 行业板块分析 - 行业涨跌幅排行和成分股分析
- 概念板块分析 - 热点概念跟踪和成分股分析
- 新股上市信息 - IPO申购和上市信息查询
- 市场指数数据 - 上证、深证、创业板等主要指数
- 风险预警股票监控 - ST和*ST股票预警
- 数据统计与可视化 - 市场概览和PE分布
- 行业产业链分析 - 产业链上下游关系展示
- 龙虎榜数据分析 - 机构交易行为分析
- 千股千评数据 - 机构评级和目标价格查询
- 涨跌停数据分析 - 每日涨跌停股票统计和分析
- 资金流向分析 - 市场、行业和个股资金流向分析
- 活跃营业部分析 - 营业部买卖行为和买卖股票分析

### 财务分析

- 财务指标数据采集与展示
  - 从2015年至今的完整财务数据
  - 智能判断数据更新时机，90天自动检查
  - 关键财务指标可视化展示
  - 财务数据对比分析
- 分红派息记录查询 - 历史分红数据和统计
- 财务报表分析 - 资产负债表、利润表、现金流量表
- 行业财务对比 - 同行业公司财务指标对比
- 股东统计分析 - 股东结构和变动分析
- 技术指标分析 - 常用技术指标计算与展示
- 行业PE分布 - 行业估值水平分析

## 技术特点

### 技术架构

- **后端框架**: Django 5.1.7
- **数据库**: MySQL (使用 mysqlclient 2.2.7)
- **数据获取**: akshare 1.16.65 (股票数据接口)
- **前端框架**: Tabler UI 组件库
- **数据可视化**: ECharts 图表库
- **数据处理**: pandas 2.2.3, numpy 2.2.4
- **异步处理**: aiohttp 3.11.14
- **其他工具**: requests, beautifulsoup4, lxml

### 数据采集

- 使用 akshare 和 Tushare 接口获取数据
- 支持增量更新，避免重复采集
- 智能判断数据更新时机（90天自动检查）
- 完善的错误处理和重试机制
- 数据采集状态监控和日志记录

### 数据展示

- 响应式布局设计，支持移动端访问
- 数据可视化展示，支持多种图表类型
- 灵活的数据筛选和排序功能
- 标准化分页组件，支持自定义每页记录数
- 统一的数据表格和表单设计

### 性能优化

- 数据库查询优化，使用索引和复合索引
- 数据库连接池管理
- 数据缓存机制，减少重复计算
- 异步数据加载，提升用户体验
- 分页性能优化，支持大数据量处理
- 数据库查询结果缓存

## 主要模块

### market_data（市场数据）

- 首页 - 市场概览、主要指数、涨跌幅榜单
- 股票列表 - 支持多条件筛选和排序
- 股票详情 - 基本信息、K线图、财务指标
- 股票历史 - 历史行情数据查询
- 行业板块列表 - 行业涨跌幅排行
- 行业板块详情 - 成分股查询
- 概念板块列表 - 概念板块涨跌幅排行
- 概念板块详情 - 成分股查询
- 产业链列表 - 产业链数据展示
- 风险预警 - ST和*ST股票监控
- 新股上市 - IPO信息查询
- 市场指数列表 - 主要指数行情
- 市场指数详情 - 指数历史数据
- 数据统计 - 市场统计和可视化
- 龙虎榜列表 - 龙虎榜交易数据
- 龙虎榜详情 - 单只股票龙虎榜记录
- 千股千评列表 - 机构评级和目标价格
- 千股千评详情 - 单只股票评级历史
- 融资融券列表 - 融资融券数据概览
- 融资融券详情 - 单只股票融资融券数据
- 涨跌停列表 - 每日涨跌停股票统计
- 市场资金流向 - 市场整体资金流向数据
- 行业资金流向 - 各行业资金流向数据
- 个股资金流向 - 个股资金流向数据
- 个股资金流向详情 - 单只股票资金流向历史
- 活跃营业部列表 - 活跃营业部交易数据
- 活跃营业部详情 - 单个营业部交易记录
- 港股通数据 - 港股通持股和成交数据

### financial_analysis（财务分析）

- 财务指标 - 关键财务指标展示和分析
- 分红记录 - 历史分红派息查询
- 财务报表 - 资产负债表、利润表、现金流量表
- 股东统计 - 股东结构和变动分析
- 股东详情 - 主要股东持股变动
- 技术指标 - 常用技术指标计算与展示
- 行业PE - 行业估值水平分析

## 关键指标展示

### 财务指标

- 行业分布统计 - 各行业股票数量和市值占比
- 市场成交量趋势 - 市场成交量和成交额变化
- 行业板块涨跌幅分布 - 各行业涨跌幅对比
- 概念板块涨跌幅分布 - 各概念涨跌幅对比
- PE分布统计 - 市场估值水平分布
- 风险预警股票监控 - ST和*ST股票列表
- 涨跌停股票统计 - 每日涨跌停数量和占比
- 市场指数走势对比 - 上证、深证、创业板指数对比
- 成交量和成交额分析 - 市场活跃度分析
- 资金流向分析 - 市场、行业和个股资金流向
- 活跃营业部分析 - 营业部交易行为分析

## 数据更新策略

### 财务数据

- 定期更新：每季度财报公布后自动更新
- 智能检测：系统每90天自动检测最新财报状态
- 增量更新：只更新新增的财务数据，减少系统负担
- 错误重试：当数据更新失败时自动重试

### 行情数据

- 实时更新：交易时段实时获取行情数据
- 定时更新：每日收盘后自动更新当日历史数据
- 历史数据：支持一次性获取历史行情数据

### 板块数据

- 定期更新：每周自动更新行业和概念板块成分股
- 实时行情：交易时段实时获取板块涨跌幅数据

### 资金流向数据

- 定时更新：每日收盘后自动更新当日资金流向数据
- 历史数据：支持一次性获取历史资金流向数据

### 活跃营业部数据

- 定时更新：每日收盘后自动更新当日活跃营业部数据
- 历史数据：支持一次性获取历史活跃营业部数据

## 安装与使用

### 环境要求

- Python 3.8+
- Django 5.1.7+
- MySQL 5.7+
- Redis (可选，用于缓存)

### 安装步骤

1. 克隆仓库

```bash
git clone https://github.com/yourusername/dj_stock.git
cd dj_stock
```

2. 创建虚拟环境

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate  # Windows
```

3. 安装依赖

```bash
pip install -r requirements.txt
```

4. 配置数据库

编辑 `dj_stock/settings.py` 文件中的数据库配置：

```python
DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": "stock_data",
        "USER": "your_username",
        "PASSWORD": "your_password",
        "HOST": "localhost",
        "PORT": "3306",
        "OPTIONS": {
            "charset": "utf8mb4",
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}
```

5. 创建日志目录

```bash
mkdir -p logs
```

6. 运行数据库迁移

```bash
python manage.py makemigrations
python manage.py migrate
```

7. 创建超级管理员

```bash
python manage.py createsuperuser
```

8. 启动开发服务器

```bash
python manage.py runserver
```

9. 访问系统

```
浏览器访问: http://127.0.0.1:8000
管理后台: http://127.0.0.1:8000/admin
```

### 数据初始化

首次使用需要初始化数据：

```bash
# 初始化股票列表
python manage.py init_stocks

# 初始化行业和概念板块
python manage.py init_sectors

# 获取历史行情数据
python manage.py fetch_history_data

# 获取财务数据
python manage.py fetch_financial_data
```

### 添加数据库字段注释

如果需要为数据库字段添加中文注释，可以运行：

```bash
python add_comments.py
```

### 刷新股票数据

可以使用以下脚本刷新股票数据：

```bash
python refresh_stock_data.py
```

## 项目结构

```
dj_stock/
├── dj_stock/              # Django项目配置
│   ├── settings.py        # 项目设置
│   ├── urls.py            # URL路由配置
│   └── wsgi.py            # WSGI配置
├── market_data/           # 市场数据应用
│   ├── models.py          # 数据模型(股票基础、板块、资金流向等)
│   ├── views/             # 视图模块
│   │   ├── stock_views.py     # 股票相关视图
│   │   ├── board_views.py     # 板块相关视图
│   │   ├── fund_flow_views.py # 资金流向视图
│   │   ├── broker_views.py    # 营业部视图
│   │   └── index_views.py     # 指数视图
│   ├── utils/             # 工具函数
│   │   ├── stock_fetcher.py   # 股票数据获取
│   │   └── pagination.py     # 分页工具
│   └── urls.py            # URL配置
├── financial_analysis/    # 财务分析应用
│   ├── models.py          # 财务数据模型
│   ├── views/             # 视图模块
│   │   ├── views.py           # 主要视图
│   │   └── shareholder_views.py # 股东视图
│   ├── utils/             # 工具函数
│   │   └── financial_fetcher.py # 财务数据获取
│   └── urls.py            # URL配置
├── static/                # 静态资源
│   ├── css/               # CSS样式(包含Tabler主题)
│   ├── js/                # JavaScript脚本
│   └── img/               # 图片资源
├── templates/             # 模板文件
│   ├── base.html          # 基础模板
│   ├── market_data/       # 市场数据模板
│   ├── financial_analysis/ # 财务分析模板
│   └── includes/          # 公共组件模板
├── logs/                  # 日志文件
├── requirements.txt       # 依赖包列表
└── manage.py              # Django管理脚本
```

## 贡献指南

欢迎提交 Issue 和 Pull Request 来帮助改进项目。

## 许可证

本项目采用 MIT 许可证。


