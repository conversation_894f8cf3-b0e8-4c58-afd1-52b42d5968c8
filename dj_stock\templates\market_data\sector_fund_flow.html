{% extends 'base.html' %}

{% block title %}行业资金流向 - 股票数据分析系统{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <h2 class="page-title">行业资金流向</h2>
        <div class="text-muted mt-1">查看各行业板块的资金流向数据</div>
      </div>

    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 行业资金流向表格 -->
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">{{ sector_type }}资金流向数据 ({{ selected_date|date:"Y-m-d" }})</h3>
        <div class="col-auto ms-auto d-print-none">
          <div class="d-flex">
            <div class="me-2">
              <form method="get" class="d-flex">
                <input type="date" class="form-control" name="date" value="{{ selected_date|date:'Y-m-d' }}" max="{{ latest_date|date:'Y-m-d' }}" />
                <select class="form-select ms-2" name="sector_type">
                  {% for type in sector_types %}
                  <option value="{{ type }}" {% if sector_type == type %}selected{% endif %}>{{ type }}</option>
                  {% endfor %}
                </select>
                <button type="submit" class="btn ms-2">查询</button>
              </form>
            </div>
          </div>
        </div>
      </div>


      <div class="table-responsive">
        <table class="table card-table table-vcenter text-nowrap datatable">
          <thead>
            <tr>
              <th>排名</th>
              <th>板块名称</th>
              <th>涨跌幅</th>
              <th>主力净流入</th>
              <th>主力净占比</th>
              <th>超大单净流入</th>
              <th>大单净流入</th>
              <th>中单净流入</th>
              <th>小单净流入</th>
              <th>领涨股</th>
            </tr>
          </thead>
          <tbody>
            {% for sector in sectors %}
            <tr>
              <td>{{ sector.rank }}</td>
              <td>{{ sector.sector_name }}</td>
              <td>
                {% if sector.net_inflow_rate > 0 %}
                <span class="text-up">+{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ sector.net_inflow_rate|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if sector.net_inflow_amount > 0 %}
                <span class="text-up">{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ sector.net_inflow_amount|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.main_net_inflow_pct > 0 %}
                <span class="text-up">{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% else %}
                <span class="text-down">{{ sector.main_net_inflow_pct|floatformat:2 }}%</span>
                {% endif %}
              </td>
              <td>
                {% if sector.super_big_net_inflow > 0 %}
                <span class="text-up">{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ sector.super_big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.big_net_inflow > 0 %}
                <span class="text-up">{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ sector.big_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.medium_net_inflow > 0 %}
                <span class="text-up">{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ sector.medium_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>
                {% if sector.small_net_inflow > 0 %}
                <span class="text-up">{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% else %}
                <span class="text-down">{{ sector.small_net_inflow|floatformat:2 }}亿</span>
                {% endif %}
              </td>
              <td>{{ sector.max_net_inflow_stock }}</td>
            </tr>
            {% empty %}
            <tr>
              <td colspan="10" class="text-center py-4">
                <div class="empty">
                  <div class="empty-img">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-database-off" width="32" height="32" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                      <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                      <path d="M12.983 8.978c3.955 -.182 7.017 -1.446 7.017 -2.978c0 -1.657 -3.582 -3 -8 -3c-1.661 0 -3.204 .19 -4.483 .515m-3.01 1.182c-.14 .214 -.507 1.304 -.507 1.303c0 .712 .916 1.388 2.53 1.913"></path>
                      <path d="M4 6v6c0 1.657 3.582 3 8 3c.986 0 1.93 -.067 2.802 -.19m3.187 -.82c1.251 -.53 2.011 -1.228 2.011 -1.99v-6"></path>
                      <path d="M4 12v6c0 1.657 3.582 3 8 3c3.217 0 5.991 -.712 7.261 -1.74m.739 -3.26v-4"></path>
                      <line x1="3" y1="3" x2="21" y2="21"></line>
                    </svg>
                  </div>
                  <p class="empty-title">暂无数据</p>
                  <p class="empty-subtitle text-muted">
                    当前日期没有行业资金流向数据，请尝试选择其他日期。
                  </p>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      {% include "includes/pagination.html" with page_obj=sectors rows_per_page=rows_per_page show_flow_indicator=True %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl)
    })
  });
</script>
{% endblock %}
