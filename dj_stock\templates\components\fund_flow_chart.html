<!-- 资金流向图表组件 -->
<div class="card chart-container">
  <div class="card-header">
    <div class="d-flex justify-content-between align-items-center">
      <h3 class="card-title">
        <i class="ti ti-chart-line me-2 text-primary"></i>
        市场资金流向趋势
      </h3>
      <div class="d-flex gap-2">
        <!-- 时间周期选择器 -->
        <select class="form-select form-select-sm" id="fund-flow-period-select" style="width: auto">
          <option value="30">近30天</option>
          <option value="60" selected>近60天</option>
          <option value="90">近90天</option>
          <option value="180">近半年</option>
          <option value="365">近一年</option>
        </select>

        <!-- 图表类型选择器 -->
        <select class="form-select form-select-sm" id="fund-flow-chart-type" style="width: auto">
          <option value="trend">趋势图</option>
          <option value="bar">柱状图</option>
          <option value="area">面积图</option>
        </select>
      </div>
    </div>
  </div>
  <div class="card-body">
    <!-- 图表容器 -->
    <div id="fund-flow-chart" style="height: 400px"></div>
  </div>
</div>

<script>
  // 资金流向图表组件
  class FundFlowChart {
    constructor(containerId, options = {}) {
      this.containerId = containerId
      this.chart = null
      this.currentPeriod = 60
      this.currentChartType = 'trend'
      this.data = []
      this.summary = {}

      this.options = {
        apiUrl: options.apiUrl || '/api/market-fund-flow-trend/',
        showLegend: options.showLegend !== false,
        showToolbox: options.showToolbox !== false,
        colors: {
          mainInflow: '#ef4444', // 红色 - 主力净流入
          superBig: '#dc2626', // 深红 - 超大单
          big: '#f97316', // 橙色 - 大单
          medium: '#eab308', // 黄色 - 中单
          small: '#22c55e', // 绿色 - 小单
          shIndex: '#3b82f6', // 蓝色 - 上证指数
          szIndex: '#8b5cf6', // 紫色 - 深证指数
        },
        ...options,
      }

      this.init()
    }

    init() {
      this.chart = echarts.init(document.getElementById(this.containerId))
      this.bindEvents()
      this.loadData()
    }

    bindEvents() {
      // 时间周期选择
      const periodSelect = document.getElementById('fund-flow-period-select')
      if (periodSelect) {
        periodSelect.addEventListener('change', (e) => {
          this.currentPeriod = parseInt(e.target.value)
          this.loadData()
        })
      }

      // 图表类型选择
      const chartTypeSelect = document.getElementById('fund-flow-chart-type')
      if (chartTypeSelect) {
        chartTypeSelect.addEventListener('change', (e) => {
          this.currentChartType = e.target.value
          this.updateChart()
        })
      }

      // 窗口大小变化时重新调整图表
      window.addEventListener('resize', () => {
        if (this.chart) {
          this.chart.resize()
        }
      })
    }

    async loadData() {
      try {
        // 显示加载状态
        this.showLoading()

        const response = await fetch(`${this.options.apiUrl}?days=${this.currentPeriod}`)
        const result = await response.json()

        if (result.success) {
          this.data = result.data
          this.summary = result.summary || {}
          this.updateChart()
          console.log('资金流向数据加载成功:', this.data.length, '条记录')
        } else {
          console.error('加载数据失败:', result.error)
          this.showError('加载数据失败: ' + result.error)
        }
      } catch (error) {
        console.error('网络请求失败:', error)
        this.showError('网络请求失败，请检查网络连接')
      }
    }

    updateChart() {
      if (!this.data || this.data.length === 0) {
        this.showNoData()
        return
      }

      const dates = this.data.map((item) => item.trade_date)
      const mainInflows = this.data.map((item) => item.main_net_inflow)
      const superBigInflows = this.data.map((item) => item.super_big_net_inflow)
      const bigInflows = this.data.map((item) => item.big_net_inflow)
      const mediumInflows = this.data.map((item) => item.medium_net_inflow)
      const smallInflows = this.data.map((item) => item.small_net_inflow)
      const shIndexChanges = this.data.map((item) => item.sh_index_change_pct)
      const szIndexChanges = this.data.map((item) => item.sz_index_change_pct)

      let option = {
        backgroundColor: 'transparent',
        title: {
          text: '市场资金流向趋势',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal',
          },
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
          formatter: this.getTooltipFormatter(),
        },
        legend: this.options.showLegend
          ? {
              data: ['主力净流入', '超大单', '大单', '中单', '小单'],
              top: 30,
              textStyle: {
                fontSize: 12,
              },
            }
          : null,
        toolbox: this.options.showToolbox
          ? {
              feature: {
                dataZoom: {
                  yAxisIndex: 'none',
                },
                restore: {},
                saveAsImage: {},
              },
              right: 20,
            }
          : null,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: this.options.showLegend ? '15%' : '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            formatter: function (value) {
              return value.substring(5) // 只显示月-日
            },
          },
        },
        yAxis: {
          type: 'value',
          name: '资金流入(亿元)',
          axisLabel: {
            formatter: '{value}',
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
        },
        series: this.getSeriesConfig(mainInflows, superBigInflows, bigInflows, mediumInflows, smallInflows),
      }

      this.chart.setOption(option, true)
    }

    getSeriesConfig(mainInflows, superBigInflows, bigInflows, mediumInflows, smallInflows) {
      const baseConfig = {
        smooth: true,
        symbol: 'circle',
        symbolSize: 4,
        lineStyle: {
          width: 2,
        },
      }

      if (this.currentChartType === 'trend') {
        return [
          {
            name: '主力净流入',
            type: 'line',
            data: mainInflows,
            itemStyle: { color: this.options.colors.mainInflow },
            ...baseConfig,
          },
          {
            name: '超大单',
            type: 'line',
            data: superBigInflows,
            itemStyle: { color: this.options.colors.superBig },
            ...baseConfig,
          },
          {
            name: '大单',
            type: 'line',
            data: bigInflows,
            itemStyle: { color: this.options.colors.big },
            ...baseConfig,
          },
          {
            name: '中单',
            type: 'line',
            data: mediumInflows,
            itemStyle: { color: this.options.colors.medium },
            ...baseConfig,
          },
          {
            name: '小单',
            type: 'line',
            data: smallInflows,
            itemStyle: { color: this.options.colors.small },
            ...baseConfig,
          },
        ]
      } else if (this.currentChartType === 'bar') {
        return [
          {
            name: '主力净流入',
            type: 'bar',
            data: mainInflows,
            itemStyle: {
              color: function (params) {
                return params.value >= 0 ? '#ef4444' : '#22c55e'
              },
            },
          },
        ]
      } else if (this.currentChartType === 'area') {
        return [
          {
            name: '主力净流入',
            type: 'line',
            data: mainInflows,
            itemStyle: { color: this.options.colors.mainInflow },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: 'rgba(239, 68, 68, 0.3)' },
                { offset: 1, color: 'rgba(239, 68, 68, 0.05)' },
              ]),
            },
            ...baseConfig,
          },
        ]
      }
    }

    getTooltipFormatter() {
      return function (params) {
        const date = params[0].name
        let html = `<div style="font-weight:bold;margin-bottom:5px;">${date}</div>`

        params.forEach((param) => {
          const value = param.value.toFixed(2)
          const color = param.value >= 0 ? '#ef4444' : '#22c55e'
          const symbol = param.value >= 0 ? '+' : ''
          html += `<div style="margin-bottom:3px;">
          ${param.seriesName}: 
          <span style="float:right;font-weight:bold;color:${color}">
            ${symbol}${value}亿
          </span>
        </div>`
        })

        return html
      }
    }

    showLoading() {
      const container = document.getElementById(this.containerId)
      if (container) {
        container.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
          </div>
        </div>
      `
      }
    }

    showError(message) {
      const container = document.getElementById(this.containerId)
      if (container) {
        container.innerHTML = `
        <div class="alert alert-danger" role="alert">
          <h4 class="alert-heading">加载失败</h4>
          <p>${message}</p>
        </div>
      `
      }
    }

    showNoData() {
      const container = document.getElementById(this.containerId)
      if (container) {
        container.innerHTML = `
        <div class="alert alert-info" role="alert">
          <h4 class="alert-heading">暂无数据</h4>
          <p>在选定的时间范围内没有找到资金流向数据</p>
        </div>
      `
      }
    }

    resize() {
      if (this.chart) {
        this.chart.resize()
      }
    }

    dispose() {
      if (this.chart) {
        this.chart.dispose()
        this.chart = null
      }
    }
  }

  // 全局函数，供外部调用
  window.initFundFlowChart = function (containerId, options) {
    return new FundFlowChart(containerId, options)
  }

  // 指数与资金流向关系图
  window.initIndexFundChart = function () {
    const indexFundChart = echarts.init(document.getElementById('index-fund-chart'))

    fetch('/api/market-fund-flow-trend/?days=60')
      .then((response) => response.json())
      .then((result) => {
        if (result.success && result.data) {
          const dates = result.data.map((item) => item.trade_date)
          const shIndex = result.data.map((item) => item.sh_index_close)
          const mainInflow = result.data.map((item) => item.main_net_inflow)

          const option = {
            backgroundColor: 'transparent',
            title: {
              text: '指数与资金流向关系',
              left: 'center',
              textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
              },
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
              },
              formatter: function (params) {
                const date = params[0].name
                let html = `<div style="font-weight:bold;margin-bottom:5px;">${date}</div>`

                params.forEach((param) => {
                  if (param.seriesName === '上证指数') {
                    html += `<div>${param.seriesName}: <span style="color:#3b82f6;">${param.value.toFixed(2)}</span></div>`
                  } else {
                    const color = param.value >= 0 ? '#ef4444' : '#22c55e'
                    const symbol = param.value >= 0 ? '+' : ''
                    html += `<div>${param.seriesName}: <span style="color:${color};">${symbol}${param.value.toFixed(2)}亿</span></div>`
                  }
                })

                return html
              },
            },
            legend: {
              data: ['上证指数', '主力净流入'],
              top: 30,
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              top: '15%',
              containLabel: true,
            },
            xAxis: {
              type: 'category',
              data: dates,
              boundaryGap: false,
              axisLabel: {
                formatter: function (value) {
                  return value.substring(5) // 只显示月-日
                },
              },
            },
            yAxis: [
              {
                type: 'value',
                name: '指数点位',
                position: 'left',
                axisLabel: {
                  formatter: '{value}',
                },
              },
              {
                type: 'value',
                name: '资金流入(亿)',
                position: 'right',
                axisLabel: {
                  formatter: '{value}亿',
                },
              },
            ],
            series: [
              {
                name: '上证指数',
                type: 'line',
                yAxisIndex: 0,
                data: shIndex,
                smooth: true,
                lineStyle: {
                  width: 2,
                  color: '#3b82f6',
                },
                symbol: 'circle',
                symbolSize: 4,
              },
              {
                name: '主力净流入',
                type: 'bar',
                yAxisIndex: 1,
                data: mainInflow,
                itemStyle: {
                  color: function (params) {
                    return params.value >= 0 ? '#ef4444' : '#22c55e'
                  },
                },
                barWidth: '60%',
              },
            ],
          }

          indexFundChart.setOption(option)
        }
      })
      .catch((error) => {
        console.error('加载指数与资金流向关系数据失败:', error)
      })
  }
</script>
