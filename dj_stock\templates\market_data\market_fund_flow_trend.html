{% extends 'base.html' %}
{% block title %}市场资金流向趋势图 - 股票数据分析系统{% endblock %}

{% block extra_css %}
<style>
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }
  
  .chart-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  }
  
  .metric-card {
    transition: transform 0.2s ease;
  }
  
  .metric-card:hover {
    transform: translateY(-2px);
  }
</style>
{% endblock %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'market_data:index' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'market_data:market_fund_flow' %}">市场资金流向</a></li>
            <li class="breadcrumb-item active" aria-current="page">趋势图</li>
          </ol>
        </nav>
        <h2 class="page-title">市场资金流向趋势图</h2>
        <div class="text-muted mt-1">查看市场资金流向的历史趋势和变化</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <a href="{% url 'market_data:market_fund_flow' %}" class="btn btn-outline-secondary">
              <i class="ti ti-arrow-left me-1"></i>返回列表
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    
    <!-- 关键指标卡片 -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">近30日累计净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="total-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">日均净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="avg-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">最大单日净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="max-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">净流入正向天数</div>
            </div>
            <div class="h2 mb-0 mt-1" id="positive-days">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主力资金流向趋势图 -->
    <div class="card chart-container mb-4">
      <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
          <h3 class="card-title">
            <i class="ti ti-chart-line me-2 text-primary"></i>
            主力资金流向趋势
          </h3>
          <div class="d-flex gap-2">
            <select class="form-select form-select-sm" id="period-select" style="width: auto">
              <option value="30">近30天</option>
              <option value="60" selected>近60天</option>
              <option value="90">近90天</option>
              <option value="180">近半年</option>
              <option value="365">近一年</option>
            </select>
          </div>
        </div>
      </div>
      <div class="card-body">
        <div id="fund-flow-chart" style="height: 400px;"></div>
      </div>
    </div>

    <!-- 资金类型分布图 -->
    <div class="row">
      <div class="col-md-6">
        <div class="card chart-container">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-chart-pie me-2 text-success"></i>
              资金类型分布
            </h3>
          </div>
          <div class="card-body">
            <div id="fund-type-chart" style="height: 300px;"></div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card chart-container">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-chart-bar me-2 text-warning"></i>
              指数与资金流向关系
            </h3>
          </div>
          <div class="card-body">
            <div id="index-fund-chart" style="height: 300px;"></div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- ECharts 库 -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

<script>
// 资金流向趋势图组件
class FundFlowTrendChart {
  constructor() {
    this.fundFlowChart = null;
    this.fundTypeChart = null;
    this.indexFundChart = null;
    this.currentPeriod = 60;
    this.data = [];
    
    this.init();
  }

  init() {
    this.initCharts();
    this.bindEvents();
    this.loadData();
  }

  initCharts() {
    // 初始化主力资金流向趋势图
    this.fundFlowChart = echarts.init(document.getElementById('fund-flow-chart'));
    
    // 初始化资金类型分布图
    this.fundTypeChart = echarts.init(document.getElementById('fund-type-chart'));
    
    // 初始化指数与资金流向关系图
    this.indexFundChart = echarts.init(document.getElementById('index-fund-chart'));
  }

  bindEvents() {
    // 时间周期选择
    document.getElementById('period-select').addEventListener('change', (e) => {
      this.currentPeriod = parseInt(e.target.value);
      this.loadData();
    });

    // 窗口大小变化时重新调整图表
    window.addEventListener('resize', () => {
      this.fundFlowChart.resize();
      this.fundTypeChart.resize();
      this.indexFundChart.resize();
    });
  }

  async loadData() {
    try {
      const response = await fetch(`/market_data/api/market-fund-flow-trend/?days=${this.currentPeriod}`);
      const result = await response.json();

      if (result.success) {
        this.data = result.data;
        this.updateCharts();
        this.updateMetrics();
      } else {
        console.error('加载数据失败:', result.error);
      }
    } catch (error) {
      console.error('网络请求失败:', error);
    }
  }

  updateMetrics() {
    if (!this.data || this.data.length === 0) return;

    // 计算关键指标
    const totalInflow = this.data.reduce((sum, item) => sum + item.main_net_inflow, 0);
    const avgInflow = totalInflow / this.data.length;
    const maxInflow = Math.max(...this.data.map(item => item.main_net_inflow));
    const positiveDays = this.data.filter(item => item.main_net_inflow > 0).length;

    // 更新指标显示
    document.getElementById('total-inflow').innerHTML = 
      `<span class="${totalInflow >= 0 ? 'text-danger' : 'text-success'}">${totalInflow.toFixed(2)}亿</span>`;
    
    document.getElementById('avg-inflow').innerHTML = 
      `<span class="${avgInflow >= 0 ? 'text-danger' : 'text-success'}">${avgInflow.toFixed(2)}亿</span>`;
    
    document.getElementById('max-inflow').innerHTML = 
      `<span class="text-danger">${maxInflow.toFixed(2)}亿</span>`;
    
    document.getElementById('positive-days').innerHTML = 
      `<span class="text-primary">${positiveDays}天</span>`;
  }

  updateCharts() {
    this.updateFundFlowChart();
    this.updateFundTypeChart();
    this.updateIndexFundChart();
  }

  updateFundFlowChart() {
    const dates = this.data.map(item => item.trade_date);
    const mainInflow = this.data.map(item => item.main_net_inflow);
    const superBigInflow = this.data.map(item => item.super_big_net_inflow);
    const bigInflow = this.data.map(item => item.big_net_inflow);

    const option = {
      title: {
        text: '主力资金流向趋势',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        formatter: function(params) {
          let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].name}</div>`;
          params.forEach(param => {
            const color = param.data >= 0 ? '#ef4444' : '#22c55e';
            result += `<div>${param.seriesName}: <span style="color: ${color};">${param.data.toFixed(2)}亿</span></div>`;
          });
          return result;
        }
      },
      legend: {
        data: ['主力净流入', '超大单净流入', '大单净流入'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: false
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          formatter: '{value}亿'
        }
      },
      series: [
        {
          name: '主力净流入',
          type: 'line',
          data: mainInflow,
          smooth: true,
          lineStyle: {
            width: 3,
            color: '#1890ff'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
              ]
            }
          },
          itemStyle: {
            color: function(params) {
              return params.data >= 0 ? '#ef4444' : '#22c55e';
            }
          }
        },
        {
          name: '超大单净流入',
          type: 'line',
          data: superBigInflow,
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#52c41a'
          }
        },
        {
          name: '大单净流入',
          type: 'line',
          data: bigInflow,
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#faad14'
          }
        }
      ]
    };

    this.fundFlowChart.setOption(option, true);
  }

  updateFundTypeChart() {
    if (!this.data || this.data.length === 0) return;

    // 计算各类型资金的总流入
    const totalSuperBig = this.data.reduce((sum, item) => sum + Math.abs(item.super_big_net_inflow), 0);
    const totalBig = this.data.reduce((sum, item) => sum + Math.abs(item.big_net_inflow), 0);
    const totalMedium = this.data.reduce((sum, item) => sum + Math.abs(item.medium_net_inflow), 0);
    const totalSmall = this.data.reduce((sum, item) => sum + Math.abs(item.small_net_inflow), 0);

    const option = {
      title: {
        text: '资金类型分布',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}亿 ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        top: 'middle'
      },
      series: [
        {
          name: '资金类型',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['60%', '50%'],
          data: [
            { value: totalSuperBig, name: '超大单', itemStyle: { color: '#ef4444' } },
            { value: totalBig, name: '大单', itemStyle: { color: '#faad14' } },
            { value: totalMedium, name: '中单', itemStyle: { color: '#1890ff' } },
            { value: totalSmall, name: '小单', itemStyle: { color: '#52c41a' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };

    this.fundTypeChart.setOption(option, true);
  }

  updateIndexFundChart() {
    const dates = this.data.map(item => item.trade_date);
    const shIndex = this.data.map(item => item.sh_index_close);
    const mainInflow = this.data.map(item => item.main_net_inflow);

    const option = {
      title: {
        text: '指数与资金流向关系',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['上证指数', '主力净流入'],
        top: 30
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: dates,
        boundaryGap: false
      },
      yAxis: [
        {
          type: 'value',
          name: '指数点位',
          position: 'left'
        },
        {
          type: 'value',
          name: '资金流入(亿)',
          position: 'right'
        }
      ],
      series: [
        {
          name: '上证指数',
          type: 'line',
          yAxisIndex: 0,
          data: shIndex,
          smooth: true,
          lineStyle: {
            width: 2,
            color: '#1890ff'
          }
        },
        {
          name: '主力净流入',
          type: 'bar',
          yAxisIndex: 1,
          data: mainInflow,
          itemStyle: {
            color: function(params) {
              return params.data >= 0 ? '#ef4444' : '#22c55e';
            }
          }
        }
      ]
    };

    this.indexFundChart.setOption(option, true);
  }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
  new FundFlowTrendChart();
});
</script>
{% endblock %}
