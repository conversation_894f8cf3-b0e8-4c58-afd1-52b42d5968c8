{% extends 'base.html' %} {% block title %}市场资金流向趋势图 - 股票数据分析系统{% endblock %} {% block extra_css %}
<style>
  .text-danger {
    color: #d63939 !important;
  }
  .text-success {
    color: #2fb344 !important;
  }

  .chart-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }

  .metric-card {
    transition: transform 0.2s ease;
  }

  .metric-card:hover {
    transform: translateY(-2px);
  }
</style>
{% endblock %} {% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row align-items-center">
      <div class="col">
        <nav aria-label="breadcrumb">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'market_data:index' %}">首页</a></li>
            <li class="breadcrumb-item"><a href="{% url 'market_data:market_fund_flow' %}">市场资金流向</a></li>
            <li class="breadcrumb-item active" aria-current="page">趋势图</li>
          </ol>
        </nav>
        <h2 class="page-title">市场资金流向趋势图</h2>
        <div class="text-muted mt-1">查看市场资金流向的历史趋势和变化</div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="d-flex">
          <div class="me-2">
            <a href="{% url 'market_data:market_fund_flow' %}" class="btn btn-outline-secondary"> <i class="ti ti-arrow-left me-1"></i>返回列表 </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <!-- 关键指标卡片 -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">近30日累计净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="total-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">日均净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="avg-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">最大单日净流入</div>
            </div>
            <div class="h2 mb-0 mt-1" id="max-inflow">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="card metric-card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="subheader">净流入正向天数</div>
            </div>
            <div class="h2 mb-0 mt-1" id="positive-days">
              <span class="text-muted">加载中...</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主力资金流向趋势图 -->
    {% include 'components/fund_flow_chart.html' %}

    <!-- 资金类型分布图 -->
    <div class="row">
      <div class="col-md-6">
        <div class="card chart-container">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-chart-pie me-2 text-success"></i>
              资金类型分布
            </h3>
          </div>
          <div class="card-body">
            <div id="fund-type-chart" style="height: 300px"></div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="card chart-container">
          <div class="card-header">
            <h3 class="card-title">
              <i class="ti ti-chart-bar me-2 text-warning"></i>
              指数与资金流向关系
            </h3>
          </div>
          <div class="card-body">
            <div id="index-fund-chart" style="height: 300px"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
  document.addEventListener('DOMContentLoaded', function () {
    // 初始化资金类型分布图
    initFundTypeChart()

    // 初始化指数与资金流向关系图
    window.initIndexFundChart()

    // 更新统计指标
    updateMetrics()
  })

  // 更新统计指标
  function updateMetrics() {
    fetch('/market_data/api/market-fund-flow-trend/?days=60')
      .then((response) => response.json())
      .then((result) => {
        if (result.success && result.summary) {
          const summary = result.summary

          // 更新指标显示
          document.getElementById('total-inflow').innerHTML = `<span class="${summary.total_main_inflow >= 0 ? 'text-danger' : 'text-success'}">
                ${summary.total_main_inflow}亿
              </span>`

          document.getElementById('avg-inflow').innerHTML = `<span class="${summary.avg_main_inflow >= 0 ? 'text-danger' : 'text-success'}">
                ${summary.avg_main_inflow}亿
              </span>`

          // 计算最大单日流入
          const maxInflow = Math.max(...result.data.map((item) => item.main_net_inflow))
          document.getElementById('max-inflow').innerHTML = `<span class="text-danger">${maxInflow.toFixed(2)}亿</span>`

          // 计算净流入天数
          const positiveDays = result.data.filter((item) => item.main_net_inflow > 0).length
          document.getElementById('positive-days').innerHTML = `<span class="text-primary">${positiveDays}天</span>`
        }
      })
      .catch((error) => {
        console.error('更新统计指标失败:', error)
      })
  }

  // 资金类型分布图
  function initFundTypeChart() {
    const fundTypeChart = echarts.init(document.getElementById('fund-type-chart'))

    fetch('/market_data/api/market-fund-flow-trend/?days=60')
      .then((response) => response.json())
      .then((result) => {
        if (result.success && result.summary && result.summary.fund_type_summary) {
          const summary = result.summary.fund_type_summary

          const option = {
            backgroundColor: 'transparent',
            title: {
              text: '资金类型分布',
              left: 'center',
              textStyle: {
                fontSize: 14,
                fontWeight: 'normal',
              },
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c}亿 ({d}%)',
            },
            legend: {
              orient: 'vertical',
              left: 'left',
              textStyle: {
                fontSize: 12,
              },
            },
            series: [
              {
                name: '资金类型',
                type: 'pie',
                radius: '50%',
                data: [
                  { value: Math.abs(summary.super_big), name: '超大单', itemStyle: { color: '#dc2626' } },
                  { value: Math.abs(summary.big), name: '大单', itemStyle: { color: '#f97316' } },
                  { value: Math.abs(summary.medium), name: '中单', itemStyle: { color: '#eab308' } },
                  { value: Math.abs(summary.small), name: '小单', itemStyle: { color: '#22c55e' } },
                ],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)',
                  },
                },
              },
            ],
          }

          fundTypeChart.setOption(option)
        }
      })
      .catch((error) => {
        console.error('加载资金类型分布数据失败:', error)
      })
  }
</script>
{% endblock %}
