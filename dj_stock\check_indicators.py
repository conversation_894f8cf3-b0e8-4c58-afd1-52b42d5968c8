import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dj_stock.settings')
import django
django.setup()
import akshare as ak
import pandas as pd

# 获取财务摘要数据
df = ak.stock_financial_abstract(symbol='600000')

# 打印所有指标
print('所有指标:')
for idx, row in df.iterrows():
    print(f"{idx}. {row['指标']}")

# 打印当前模型中的字段
from financial_analysis.models import StockFinancialIndicator
model_fields = [field.name for field in StockFinancialIndicator._meta.fields 
                if not field.name.startswith('_') and field.name not in ['id', 'stock_code', 'report_date', 'last_update', 'data_source', 'collection_date']]
print('\n当前模型字段:')
for field in model_fields:
    print(field)
