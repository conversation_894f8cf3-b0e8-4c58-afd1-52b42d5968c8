{% extends "base.html" %}
{% load static %}

{% block extra_css %}

{% endblock %}

{% block title %}财务筛选{% endblock %}

{% block content %}
{% csrf_token %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="card mb-2">
                            <div class="card-header bg-light py-2">
                                <h6 class="mb-0"><i class="ti ti-filter me-1"></i>基本筛选</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <!-- 行业选择 -->
                                    <div class="col-md-3">
                                        <label for="industry" class="form-label small">行业</label>
                                        <select class="form-select form-select-sm" id="industry" name="industry">
                                            <option value="">全部行业</option>
                                            {% for ind in industries %}
                                            <option value="{{ ind }}" {% if ind == industry %}selected{% endif %}>{{ ind }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>

                                    <!-- 市值范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">市值范围（亿元）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="market_cap_min" placeholder="最小值" value="{{ market_cap_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="market_cap_max" placeholder="最大值" value="{{ market_cap_max }}">
                                        </div>
                                    </div>

                                    <!-- 流通市值范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">流通市值（亿元）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="float_market_cap_min" placeholder="最小值" value="{{ float_market_cap_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="float_market_cap_max" placeholder="最大值" value="{{ float_market_cap_max }}">
                                        </div>
                                    </div>

                                    <!-- PE范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">PE范围</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="pe_min" placeholder="最小值" value="{{ pe_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="pe_max" placeholder="最大值" value="{{ pe_max }}">
                                        </div>
                                    </div>

                                    <!-- PB范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">PB范围</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="pb_min" placeholder="最小值" value="{{ pb_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="pb_max" placeholder="最大值" value="{{ pb_max }}">
                                        </div>
                                    </div>

                                    <!-- 搜索框 -->
                                    <div class="col-md-3">
                                        <label for="search" class="form-label small">搜索股票代码/名称</label>
                                        <div class="input-group input-group-sm">
                                            <span class="input-group-text"><i class="ti ti-search"></i></span>
                                            <input type="text" class="form-control" id="search" name="search" value="{{ search }}" placeholder="输入股票代码或名称">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-2">
                            <div class="card-header bg-light py-2">
                                <h6 class="mb-0"><i class="ti ti-chart-bar me-1"></i>财务指标筛选</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <!-- ROE范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">ROE范围（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="roe_min" placeholder="最小值" value="{{ roe_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="roe_max" placeholder="最大值" value="{{ roe_max }}">
                                        </div>
                                    </div>

                                    <!-- 净利润增长率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">净利润增长率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="net_profit_growth_min" placeholder="最小值" value="{{ net_profit_growth_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="net_profit_growth_max" placeholder="最大值" value="{{ net_profit_growth_max }}">
                                        </div>
                                    </div>

                                    <!-- 营收增长率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">营收增长率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="revenue_growth_min" placeholder="最小值" value="{{ revenue_growth_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="revenue_growth_max" placeholder="最大值" value="{{ revenue_growth_max }}">
                                        </div>
                                    </div>

                                    <!-- 毛利率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">毛利率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="gross_profit_margin_min" placeholder="最小值" value="{{ gross_profit_margin_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="gross_profit_margin_max" placeholder="最大值" value="{{ gross_profit_margin_max }}">
                                        </div>
                                    </div>

                                    <!-- 负债率范围 -->
                                    <div class="col-md-3">
                                        <label class="form-label small">负债率（%）</label>
                                        <div class="input-group input-group-sm">
                                            <input type="number" class="form-control" name="debt_ratio_min" placeholder="最小值" value="{{ debt_ratio_min }}">
                                            <span class="input-group-text py-0">-</span>
                                            <input type="number" class="form-control" name="debt_ratio_max" placeholder="最大值" value="{{ debt_ratio_max }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mb-2">
                            <div class="card-header bg-light py-2">
                                <h6 class="mb-0"><i class="ti ti-star me-1"></i>高级筛选</h6>
                            </div>
                            <div class="card-body py-2">
                                <div class="row g-2">
                                    <!-- 连续3年ROE > 15% -->
                                    <div class="col-md-4">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="continuous_roe" name="continuous_roe" value="1" {% if continuous_roe %}checked{% endif %}>
                                            <label class="form-check-label small" for="continuous_roe">连续3年ROE > 15%</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 按钮组 -->
                        <div class="col-12 text-center mt-2 mb-2">
                            <button type="submit" class="btn btn-primary btn-sm px-3">
                                <i class="ti ti-search me-1"></i>开始筛选
                            </button>
                            <a href="{% url 'financial_analysis:financial_screener' %}" class="btn btn-outline-secondary btn-sm px-3 ms-2">
                                <i class="ti ti-refresh me-1"></i>重置条件
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 股票列表 -->
            <div class="card mt-4 shadow-sm">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center py-2">
                    <div class="d-flex align-items-center">
                        <i class="ti ti-table me-1"></i>
                        <h6 class="card-title mb-0">筛选结果</h6>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="badge bg-white text-primary me-2">
                            <i class="ti ti-chart-bar me-1"></i>
                            {{ page_obj.paginator.count }}支
                        </div>
                        <div class="text-white small me-2" style="font-size: 0.75rem;">
                            <i class="ti ti-calendar me-1"></i>
                            {{ latest_trade_date|date:"Y-m-d" }}
                        </div>
                        <div class="text-white small" style="font-size: 0.75rem;">
                            <i class="ti ti-report-money me-1"></i>
                            {{ latest_report_date|date:"Y-m-d" }}
                        </div>
                    </div>
                </div>
                <div class="card-body p-2">
                    <div class="table-responsive">
                        <table class="table table-hover table-striped table-sm">
                            <thead class="table-light">
                                <tr style="font-size: 0.75rem;">
                                    <th width="40px">收藏</th>
                                    <th><a href="?sort=stock_code&order={% if sort_by == 'stock_code' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">代码 <i class="sort-icon {% if sort_by == 'stock_code' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=stock_name&order={% if sort_by == 'stock_name' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">名称 <i class="sort-icon {% if sort_by == 'stock_name' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=industry&order={% if sort_by == 'industry' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">行业 <i class="sort-icon {% if sort_by == 'industry' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=market_cap&order={% if sort_by == 'market_cap' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">市值(亿) <i class="sort-icon {% if sort_by == 'market_cap' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=pe_ratio&order={% if sort_by == 'pe_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PE(倍) <i class="sort-icon {% if sort_by == 'pe_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=industry_avg_pe&order={% if sort_by == 'industry_avg_pe' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">行业PE(倍) <i class="sort-icon {% if sort_by == 'industry_avg_pe' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=pb_ratio&order={% if sort_by == 'pb_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">PB(倍) <i class="sort-icon {% if sort_by == 'pb_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=latest_price&order={% if sort_by == 'latest_price' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">最新价(元) <i class="sort-icon {% if sort_by == 'latest_price' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=change_percent&order={% if sort_by == 'change_percent' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">涨跌幅(%) <i class="sort-icon {% if sort_by == 'change_percent' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=roe&order={% if sort_by == 'roe' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">ROE(%) <i class="sort-icon {% if sort_by == 'roe' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=net_profit_growth&order={% if sort_by == 'net_profit_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">净利润增(%) <i class="sort-icon {% if sort_by == 'net_profit_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=revenue_growth&order={% if sort_by == 'revenue_growth' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">营收增(%) <i class="sort-icon {% if sort_by == 'revenue_growth' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=gross_profit_margin&order={% if sort_by == 'gross_profit_margin' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">毛利率(%) <i class="sort-icon {% if sort_by == 'gross_profit_margin' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=debt_ratio&order={% if sort_by == 'debt_ratio' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">负债率(%) <i class="sort-icon {% if sort_by == 'debt_ratio' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th><a href="?sort=float_market_cap&order={% if sort_by == 'float_market_cap' and sort_order == 'asc' %}desc{% else %}asc{% endif %}{% for key, value in request.GET.items %}{% if key != 'sort' and key != 'order' and key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="sort-link">流通市值(亿) <i class="sort-icon {% if sort_by == 'float_market_cap' %}{% if sort_order == 'asc' %}ti ti-arrow-up text-primary{% else %}ti ti-arrow-down text-primary{% endif %}{% else %}ti ti-arrows-sort text-muted opacity-50{% endif %}"></i></a></th>
                                    <th>3年ROE>15%</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for stock in page_obj %}
                                <tr class="align-middle" style="font-size: 0.75rem;">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <button class="btn btn-sm btn-warning favorite-btn"
                                                    data-code="{{ stock.stock_code }}"
                                                    data-name="{{ stock.stock_name }}"
                                                    title="添加到收藏">
                                                <i class="ti ti-star"></i>
                                            </button>
                                            <span class="favorite-icon ms-1 d-none" data-code="{{ stock.stock_code }}">
                                                <i class="ti ti-star-filled text-warning" style="font-size: 0.8rem;"></i>
                                            </span>
                                        </div>
                                    </td>
                                    <td><a href="{% url 'market_data:stock_detail' stock.stock_code %}" class="text-primary fw-semibold">{{ stock.stock_code }}</a></td>
                                    <td><span class="fw-medium">{{ stock.stock_name }}</span></td>
                                    <td><span class="badge bg-light-info text-info rounded-pill px-1" style="font-size: 0.7rem;">{{ stock.industry }}</span></td>
                                    <td>{% if stock.market_cap %}{{ stock.market_cap|floatformat:1 }}亿{% else %}-{% endif %}</td>
                                    <td>{% if stock.pe_ratio %}{{ stock.pe_ratio|floatformat:1 }}倍{% else %}-{% endif %}</td>
                                    <td>
                                        {% if stock.industry_avg_pe %}
                                            {% if stock.pe_ratio %}
                                                {% with pe_ratio=stock.pe_ratio|floatformat:1|stringformat:"s" industry_pe=stock.industry_avg_pe|floatformat:1|stringformat:"s" %}
                                                    {% if stock.pe_ratio > stock.industry_avg_pe %}
                                                        <span class="badge bg-warning-subtle text-warning" style="font-size: 0.7rem;">{{ pe_ratio }}/{{ industry_pe }}倍</span>
                                                    {% elif stock.pe_ratio < stock.industry_avg_pe %}
                                                        <span class="badge bg-success-subtle text-success" style="font-size: 0.7rem;">{{ pe_ratio }}/{{ industry_pe }}倍</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary-subtle text-secondary" style="font-size: 0.7rem;">{{ pe_ratio }}/{{ industry_pe }}倍</span>
                                                    {% endif %}
                                                {% endwith %}
                                            {% else %}
                                                <span class="text-muted">{{ stock.industry_avg_pe|floatformat:1 }}倍</span>
                                            {% endif %}
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>{% if stock.pb_ratio %}{{ stock.pb_ratio|floatformat:1 }}倍{% else %}-{% endif %}</td>
                                    <td>
                                        {% if stock.latest_price %}
                                            <span class="fw-medium">{{ stock.latest_price|floatformat:2 }}元</span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.change_percent != None %}
                                            <span class="badge {% if stock.change_percent > 0 %}bg-danger-subtle text-danger{% elif stock.change_percent < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                                {% if stock.change_percent > 0 %}+{% endif %}{{ stock.change_percent|floatformat:2 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td><span class="fw-medium">{% if stock.roe != None %}{{ stock.roe|floatformat:1 }}%{% else %}-{% endif %}</span></td>
                                    <td>
                                        {% if stock.net_profit_growth != None %}
                                            <span class="badge {% if stock.net_profit_growth > 0 %}bg-danger-subtle text-danger{% elif stock.net_profit_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                                {% if stock.net_profit_growth > 0 %}+{% endif %}{{ stock.net_profit_growth|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>
                                        {% if stock.revenue_growth != None %}
                                            <span class="badge {% if stock.revenue_growth > 0 %}bg-danger-subtle text-danger{% elif stock.revenue_growth < 0 %}bg-success-subtle text-success{% else %}bg-secondary-subtle text-secondary{% endif %}" style="font-size: 0.7rem;">
                                                {% if stock.revenue_growth > 0 %}+{% endif %}{{ stock.revenue_growth|floatformat:1 }}%
                                            </span>
                                        {% else %}-{% endif %}
                                    </td>
                                    <td>{% if stock.gross_profit_margin != None %}{{ stock.gross_profit_margin|floatformat:1 }}%{% else %}-{% endif %}</td>
                                    <td>{% if stock.debt_ratio != None %}{{ stock.debt_ratio|floatformat:1 }}%{% else %}-{% endif %}</td>
                                    <td>{% if stock.float_market_cap != None %}{{ stock.float_market_cap|floatformat:1 }}亿{% else %}-{% endif %}</td>
                                    <td>{% if stock.continuous_roe %}<span class="badge bg-success-subtle text-success" style="font-size: 0.7rem;">是</span>{% else %}<span class="badge bg-secondary-subtle text-secondary" style="font-size: 0.7rem;">否</span>{% endif %}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="17" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="ti ti-search-off fs-1 text-muted mb-3"></i>
                                            <p class="mb-1 fw-medium">没有找到符合条件的股票</p>
                                            <p class="text-muted small">请尝试调整筛选条件</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="mt-4">
                        {% include "includes/pagination.html" with page_obj=page_obj rows_per_page=rows_per_page %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .sort-link {
                color: inherit;
                text-decoration: none;
                cursor: pointer;
                width: 100%;
                display: inline-flex;
                align-items: center;
                justify-content: space-between;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                transition: background-color 0.2s;
            }
            .sort-link:hover { color: #0d6efd; background-color: rgba(13, 110, 253, 0.05); }
            .sort-icon { margin-left: 0.5rem; opacity: 0.5; transition: opacity 0.2s; }
            .sort-link:hover .sort-icon { opacity: 1; }
            th {
                white-space: nowrap;
                transition: background-color 0.3s;
                padding: 0.4rem 0.5rem !important;
                font-weight: 500;
            }
            td { padding: 0.3rem 0.5rem !important; }
            th:hover { background-color: rgba(13, 110, 253, 0.05); }
            .bg-light-primary { background-color: rgba(13, 110, 253, 0.1); }
            .bg-light-info { background-color: rgba(13, 202, 240, 0.1); }
            .rounded-pill { border-radius: 50rem; }
            .table-sm { font-size: 0.75rem; }
            .card-body.p-2 .table { margin-bottom: 0.5rem; }
            .favorite-icon { cursor: pointer; }
            .favorite-icon.active { display: inline-block !important; }
            @keyframes pulse {
                0% { box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7); }
                70% { box-shadow: 0 0 0 10px rgba(25, 135, 84, 0); }
                100% { box-shadow: 0 0 0 0 rgba(25, 135, 84, 0); }
            }
            .pulse-animation { animation: pulse 1.5s infinite; }
            .favorite-btn.disabled { opacity: 0.6; cursor: not-allowed; }
        `;
        document.head.appendChild(style);

        // 元素引用
        const favoriteIcons = document.querySelectorAll('.favorite-icon');
        const favoriteBtns = document.querySelectorAll('.favorite-btn');

        // 已收藏的股票列表
        let favoriteStocks = [];

        // 显示通知
        function showNotification(type, title, message) {
            const notification = document.createElement('div');
            notification.className = 'toast show position-fixed top-0 end-0 m-3';
            notification.style.zIndex = '9999';
            notification.innerHTML = `
                <div class="toast-header bg-${type} text-white">
                    <i class="ti ti-${type === 'success' ? 'star' : 'alert-triangle'} me-2"></i>
                    <strong class="me-auto">${title}</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">${message}</div>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 500);
            }, 3000);
        }

        // 获取已收藏的股票
        function loadFavoriteStocks() {
            fetch('{% url "financial_analysis:get_favorite_stocks" %}')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        favoriteStocks = data.favorites;
                        updateFavoriteIcons();
                        updateFavoriteButtons();
                    }
                })
                .catch(error => {
                    console.error('Error loading favorites:', error);
                });
        }

        // 更新收藏图标显示
        function updateFavoriteIcons() {
            favoriteIcons.forEach(icon => {
                const stockCode = icon.dataset.code;
                if (favoriteStocks.includes(stockCode)) {
                    icon.classList.remove('d-none');
                    icon.classList.add('active');
                } else {
                    icon.classList.add('d-none');
                    icon.classList.remove('active');
                }
            });
        }

        // 更新收藏按钮状态
        function updateFavoriteButtons() {
            favoriteBtns.forEach(btn => {
                const stockCode = btn.dataset.code;
                if (favoriteStocks.includes(stockCode)) {
                    btn.classList.remove('btn-warning');
                    btn.classList.add('btn-success');
                    btn.setAttribute('title', '已收藏');
                    btn.disabled = true;
                    btn.classList.add('disabled');
                } else {
                    btn.classList.add('btn-warning');
                    btn.classList.remove('btn-success');
                    btn.setAttribute('title', '添加到收藏');
                    btn.disabled = false;
                    btn.classList.remove('disabled');
                }
            });
        }

        // 添加收藏按钮点击事件
        favoriteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const stockCode = this.dataset.code;
                const stockName = this.dataset.name;

                // 如果已经收藏过，则不再处理
                if (favoriteStocks.includes(stockCode)) {
                    return;
                }

                // 显示加载状态
                this.innerHTML = '<i class="ti ti-loader ti-spin"></i>';
                this.disabled = true;

                // 发送AJAX请求
                fetch('{% url "financial_analysis:ajax_add_favorite" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': '{{ csrf_token }}'
                    },
                    body: JSON.stringify({
                        stock_code: stockCode,
                        stock_name: stockName
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 成功处理
                        showNotification('success', '收藏成功', data.message);

                        // 如果是新添加的收藏，则更新收藏列表
                        if (data.created && !favoriteStocks.includes(data.stock_code)) {
                            favoriteStocks.push(data.stock_code);
                        }

                        // 更新UI
                        updateFavoriteIcons();
                        updateFavoriteButtons();
                    } else {
                        // 失败处理
                        showNotification('danger', '收藏失败', data.message);
                        this.innerHTML = '<i class="ti ti-star"></i>';
                        this.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('danger', '收藏失败', '网络错误，请稍后再试');
                    this.innerHTML = '<i class="ti ti-star"></i>';
                    this.disabled = false;
                });
            });
        });

        // 初始化
        loadFavoriteStocks();
    });
</script>
{% endblock %}