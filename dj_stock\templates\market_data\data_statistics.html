{% extends "base.html" %} {% load static %} {% block title %}数据统计 | 股票数据分析系统{% endblock %} {% block extra_css %}
<style>
  .chart-container {
    height: 380px;
    margin-bottom: 20px;
    position: relative;
  }
  .card {
    transition: all 0.3s;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: rgba(0, 0, 0, 0.05) 0 4px 12px;
    border: none;
    margin-bottom: 20px;
  }
  .card:hover {
    transform: translateY(-5px);
    box-shadow: rgba(0, 0, 0, 0.1) 0 10px 20px;
  }
  .card-header {
    background-color: #f8fafc;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 15px 20px;
  }
  .card-header .card-title {
    font-weight: 600;
    font-size: 16px;
    margin: 0;
    display: flex;
    align-items: center;
  }
  .card-header .card-title i {
    margin-right: 8px;
    opacity: 0.7;
  }
  .card-body {
    padding: 0;
  }
  .stat-summary {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
  }
  .stat-item {
    flex: 1;
    min-width: 200px;
    padding: 20px;
    border-radius: 10px;
    background: #fff;
    box-shadow: rgba(0, 0, 0, 0.05) 0 2px 8px;
    margin-right: 15px;
    margin-bottom: 15px;
    border-left: 4px solid var(--tblr-primary);
  }
  .stat-item h3 {
    font-size: 14px;
    color: #718096;
    margin: 0 0 10px 0;
  }
  .stat-item .value {
    font-size: 28px;
    font-weight: 600;
    color: #1e293b;
  }
  .stat-item .change {
    font-size: 13px;
    margin-top: 5px;
  }
  .stat-item .change.positive {
    color: #f56c6c;
  }
  .stat-item .change.negative {
    color: #67c23a;
  }
  .stat-item .small-text {
    font-size: 12px;
    color: #718096;
    margin-top: 5px;
    margin-bottom: 5px;
  }
  .chart-filters {
    position: absolute;
    top: 15px;
    right: 20px;
    z-index: 10;
  }
  .chart-filters select {
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 5px 10px;
    font-size: 13px;
    background-color: #fff;
  }
  .data-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .filter-group {
    display: flex;
    align-items: center;
  }
  .filter-group label {
    margin-right: 10px;
    font-size: 14px;
    font-weight: 500;
  }
  .filter-group select,
  .filter-group input {
    margin-right: 15px;
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    padding: 6px 12px;
  }
</style>
{% endblock %} {% block content %}
<div class="container-xl">
  <!-- 页面标题 -->
  <div class="page-header d-print-none">
    <div class="row align-items-center">
      <div class="col">
        <div class="page-pretitle">数据分析</div>
        <h2 class="page-title">市场数据统计</h2>
      </div>
      <div class="col-auto ms-auto">
        <div class="d-flex">
          <div class="btn-list">
            <span class="d-none d-sm-inline">
              <a href="/" class="btn btn-outline-primary">
                <i class="ti ti-home"></i>
                返回首页
              </a>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 统计摘要 -->
  <div class="stat-summary">
    <div class="stat-item">
      <h3>市场平均市盈率</h3>
      <div class="value">{{ avg_pe|default:"--" }}</div>
      <div class="change {% if pe_change >= 0 %}positive{% else %}negative{% endif %}">
        {{ pe_change|default:"0" }}% <i class="ti ti-arrow-{% if pe_change >= 0 %}up{% else %}down{% endif %}"></i>
      </div>
    </div>
    <div class="stat-item">
      <h3>市场平均市净率</h3>
      <div class="value">{{ avg_pb|default:"--" }}</div>
      <div class="change {% if pb_change >= 0 %}positive{% else %}negative{% endif %}">
        {{ pb_change|default:"0" }}% <i class="ti ti-arrow-{% if pb_change >= 0 %}up{% else %}down{% endif %}"></i>
      </div>
    </div>
    <div class="stat-item">
      <h3>上涨/下跌比例</h3>
      <div class="value">{{ up_down_ratio|default:"--" }}</div>
      <div class="small-text">上涨: {{ up_percent|default:"0" }}% 下跌: {{ down_percent|default:"0" }}%</div>
      <div class="change {% if ratio_change >= 0 %}positive{% else %}negative{% endif %}">
        {{ ratio_change|default:"0" }}% <i class="ti ti-arrow-{% if ratio_change >= 0 %}up{% else %}down{% endif %}"></i>
      </div>
    </div>
    <div class="stat-item">
      <h3>成交量变化</h3>
      <div class="value">{{ volume_change|default:"--" }}%</div>
      <div class="change {% if volume_change >= 0 %}positive{% else %}negative{% endif %}">
        <i class="ti ti-arrow-{% if volume_change >= 0 %}up{% else %}down{% endif %}"></i>
      </div>
    </div>
  </div>

  <div class="row row-deck row-cards">
    <!-- 股票总数和行业分布 -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="ti ti-building"></i> 行业分布</h3>
        </div>
        <div class="card-body">
          <div id="industry-chart" class="chart-container"></div>
        </div>
      </div>
    </div>

    <!-- 市场成交量趋势 -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="ti ti-chart-line"></i> 市场成交趋势 (30天)</h3>
          <div class="chart-filters">
            <select id="volume-trend-filter">
              <option value="volume">成交量(亿手)</option>
              <option value="amount">成交额(亿元)</option>
            </select>
          </div>
        </div>
        <div class="card-body">
          <div id="volume-trend-chart" class="chart-container"></div>
        </div>
      </div>
    </div>

    <!-- 行业板块涨跌幅分布 -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="ti ti-trending-up"></i> 行业板块涨跌幅分布
            <span class="text-muted ms-2 fs-6">{% if latest_date %}({{ latest_date }}){% endif %}</span>
          </h3>
          <div class="chart-filters">
            <select id="industry-performance-filter">
              <option value="top">涨幅前20</option>
              <option value="bottom">跌幅前20</option>
            </select>
          </div>
        </div>
        <div class="card-body">
          <div id="industry-performance-chart" class="chart-container"></div>
        </div>
      </div>
    </div>

    <!-- 概念板块涨跌幅分布 -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">
            <i class="ti ti-bulb"></i> 概念板块涨跌幅分布
            <span class="text-muted ms-2 fs-6">{% if concept_latest_date %}({{ concept_latest_date }}){% endif %}</span>
          </h3>
          <div class="chart-filters">
            <select id="concept-performance-filter">
              <option value="top">涨幅前20</option>
              <option value="bottom">跌幅前20</option>
            </select>
          </div>
        </div>
        <div class="card-body">
          <div id="concept-performance-chart" class="chart-container"></div>
        </div>
      </div>
    </div>

    <!-- PE分布 -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="ti ti-chart-pie"></i> 市场PE分布</h3>
        </div>
        <div class="card-body">
          <div id="pe-distribution-chart" class="chart-container"></div>
        </div>
      </div>
    </div>

    <!-- 市场指数趋势 -->
    <div class="col-md-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title"><i class="ti ti-chart-line"></i> 市场指数趋势 <span id="date-range-display">(30天)</span></h3>
          <div class="chart-filters">
            <div class="d-flex align-items-center">
              <select id="market-index-filter" class="me-3">
                <option value="000001">上证指数</option>
                <option value="399001">深证成指</option>
                <option value="000300">沪深300</option>
              </select>
              <select id="date-range-filter" class="me-2">
                <option value="7">近7天</option>
                <option value="14">近14天</option>
                <option value="30" selected>近30天</option>
                <option value="60">近60天</option>
                <option value="90">近90天</option>
                <option value="180">近半年</option>
                <option value="365">近一年</option>
                <option value="custom">自定义区间</option>
              </select>
              <div id="custom-date-range" style="display: none" class="d-flex align-items-center">
                <input type="date" id="start-date" class="form-control form-control-sm me-1" style="width: 140px" />
                <span class="mx-1">-</span>
                <input type="date" id="end-date" class="form-control form-control-sm me-1" style="width: 140px" />
                <button id="apply-date-range" class="btn btn-sm btn-primary">应用</button>
              </div>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div id="market-index-chart" class="chart-container"></div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<!-- 引入 ECharts -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script src="/static/js/market_index_chart.js"></script>
<script>
  // 主函数
  document.addEventListener('DOMContentLoaded', function () {
    // 解析JSON数据
    const industryStats = JSON.parse('{{ industry_stats|safe }}')
    const volumeTrend = JSON.parse('{{ volume_trend|safe }}')
    const industryPerformance = JSON.parse('{{ industry_performance|safe }}')
    const conceptPerformance = JSON.parse('{{ concept_performance|safe }}')
    const peDistribution = JSON.parse('{{ pe_distribution|safe }}')

    // 主题颜色配置
    const colorPalette = [
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
      '#206bc4',
      '#6574cd',
      '#38c172',
      '#ffbb00',
      '#f66d9b',
      '#6cb2eb',
    ]

    const riseColor = '#f56c6c'
    const fallColor = '#67c23a'

    // 行业分布饼图
    const industryChart = echarts.init(document.getElementById('industry-chart'))
    const industryOption = {
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          return (
            `<div style="font-weight:bold;margin-bottom:3px;">${params.name}</div>` +
            `<div>股票数量: <span style="font-weight:bold;float:right;margin-left:20px;">${params.value}</span></div>` +
            `<div>占比: <span style="font-weight:bold;float:right;margin-left:20px;">${params.percent.toFixed(2)}%</span></div>`
          )
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#eee',
        borderWidth: 1,
        padding: 10,
        textStyle: { color: '#333' },
        extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);',
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 10,
        top: 20,
        bottom: 20,
        textStyle: { fontSize: 12 },
        pageIconColor: '#5470c6',
        pageIconInactiveColor: '#aaa',
        pageTextStyle: { color: '#666' },
        formatter: function (name) {
          // 限制图例文本长度
          return name.length > 10 ? name.substring(0, 10) + '...' : name
        },
      },
      series: [
        {
          name: '行业分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold',
              formatter: '{b}: {c} ({d}%)',
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              borderColor: '#eee',
              borderWidth: 1,
              padding: [4, 8],
              borderRadius: 4,
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
          data: industryStats.map(function (item, index) {
            return {
              name: item.industry,
              value: item.count,
              itemStyle: { color: colorPalette[index % colorPalette.length] },
            }
          }),
        },
      ],
    }
    industryChart.setOption(industryOption)

    // 市场成交量趋势图
    const volumeTrendChart = echarts.init(document.getElementById('volume-trend-chart'))
    function updateVolumeTrendChart(dataType = 'volume') {
      const volumeTrendOption = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999',
            },
          },
          formatter: function (params) {
            const date = params[0].name
            const value = params[0].value.toFixed(2)
            const unit = dataType === 'volume' ? '亿手' : '亿元'

            // 计算与前一交易日的变化百分比
            let changePercent = 0
            const index = params[0].dataIndex
            if (index > 0) {
              const prevValue = volumeTrend[index - 1][dataType]
              if (prevValue > 0) {
                changePercent = (((value - prevValue) / prevValue) * 100).toFixed(2)
              }
            }

            const changeColor = changePercent >= 0 ? '#f56c6c' : '#67c23a'
            const changeSymbol = changePercent >= 0 ? '+' : ''

            return (
              `<div style="font-weight:bold;margin-bottom:8px;">${date}</div>` +
              `<div style="margin-bottom:5px;">` +
              `<span style="display:inline-block;margin-right:5px;border-radius:50%;width:10px;height:10px;background-color:${params[0].color};"></span>` +
              `<span>${params[0].seriesName}: </span>` +
              `<span style="float:right;font-weight:bold;">${value}${unit}</span>` +
              `</div>` +
              `<div>` +
              `<span>环比变化: </span>` +
              `<span style="float:right;font-weight:bold;color:${changeColor}">${changeSymbol}${changePercent}%</span>` +
              `</div>`
            )
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#eee',
          borderWidth: 1,
          padding: 10,
          textStyle: { color: '#333' },
          extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);',
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '8%',
          top: '10%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: volumeTrend.map(function (item) {
            return item.date
          }),
          axisLabel: {
            rotate: 30,
            fontSize: 12,
            color: '#718096',
            formatter: function (value) {
              // 只显示月-日
              return value.substring(5)
            },
          },
          axisLine: {
            lineStyle: {
              color: '#eaecef',
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          name: dataType === 'volume' ? '成交量(亿手)' : '成交额(亿元)',
          nameTextStyle: {
            color: '#718096',
            fontSize: 12,
            padding: [0, 0, 0, 40], // 增加右侧填充，避免名称被截断
          },
          axisLabel: {
            color: '#718096',
            fontSize: 12,
            formatter: function (value) {
              // 格式化数字，增加可读性
              if (value >= 10) {
                return value.toFixed(0)
              } else {
                return value.toFixed(1)
              }
            },
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eaecef',
              type: 'dashed',
            },
          },
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100,
            zoomLock: false,
          },
          {
            type: 'slider',
            show: true,
            start: 0,
            end: 100,
            height: 20,
            bottom: 0,
            borderColor: 'transparent',
            fillerColor: 'rgba(84, 112, 198, 0.2)',
            handleStyle: {
              color: '#5470c6',
              borderColor: '#5470c6',
            },
            textStyle: {
              color: '#718096',
            },
            handleSize: '120%',
          },
        ],
        series: [
          {
            name: dataType === 'volume' ? '成交量' : '成交额',
            type: 'bar',
            data: volumeTrend.map(function (item, index) {
              // 计算与前一交易日的变化
              let change = 0
              if (index > 0) {
                const prevValue = volumeTrend[index - 1][dataType]
                change = item[dataType] - prevValue
              }

              return {
                value: item[dataType],
                change: change,
                itemStyle: {
                  // 根据环比变化决定颜色
                  color: change >= 0 ? '#f56c6c' : '#67c23a',
                },
              }
            }),
            itemStyle: {
              borderRadius: [4, 4, 0, 0],
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.3)',
              },
            },
            markLine: {
              symbol: 'none',
              lineStyle: {
                color: '#5470c6',
                type: 'dashed',
                width: 1.5,
              },
              label: {
                position: 'end',
                formatter: '{b}: {c}',
                backgroundColor: 'rgba(255, 255, 255, 0.8)',
                padding: [4, 8],
                borderRadius: 4,
                color: '#333',
              },
              data: [{ type: 'average', name: '平均值' }],
            },
          },
        ],
      }
      volumeTrendChart.setOption(volumeTrendOption, true)
    }
    updateVolumeTrendChart('volume')

    // 排序行业板块数据
    function sortPerformanceData(data, isTop) {
      return [...data].sort((a, b) => (isTop ? b.change_percent - a.change_percent : a.change_percent - b.change_percent)).slice(0, 20)
    }

    // 行业板块涨跌幅分布图
    const industryPerformanceChart = echarts.init(document.getElementById('industry-performance-chart'))
    function updateIndustryPerformanceChart(isTop = true) {
      const sortedData = sortPerformanceData(industryPerformance, isTop)

      const industryPerformanceOption = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params) {
            return params[0].name + '<br/>' + params[0].value.toFixed(2) + '%'
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#eee',
          borderWidth: 1,
          textStyle: { color: '#333' },
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '8%',
          top: '5%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          name: '涨跌幅(%)',
          nameTextStyle: {
            color: '#718096',
            fontSize: 12,
          },
          axisLabel: {
            formatter: '{value}%',
            color: '#718096',
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: '#eaecef',
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eaecef',
              type: 'dashed',
            },
          },
        },
        yAxis: {
          type: 'category',
          data: sortedData.map((item) => item.board_name || item.name),
          axisLabel: {
            fontSize: 12,
            color: '#718096',
          },
          axisLine: {
            lineStyle: {
              color: '#eaecef',
            },
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: '涨跌幅',
            type: 'bar',
            data: sortedData.map((item) => item.change_percent),
            itemStyle: {
              color: function (params) {
                return params.data >= 0 ? riseColor : fallColor
              },
              borderRadius: [0, 4, 4, 0],
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{c}%',
              fontSize: 12,
              color: '#718096',
            },
            barWidth: '60%',
          },
        ],
      }
      industryPerformanceChart.setOption(industryPerformanceOption, true)
    }
    updateIndustryPerformanceChart(true)

    // 概念板块涨跌幅分布图
    const conceptPerformanceChart = echarts.init(document.getElementById('concept-performance-chart'))
    function updateConceptPerformanceChart(isTop = true) {
      const sortedData = sortPerformanceData(conceptPerformance, isTop)

      const conceptPerformanceOption = {
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
          formatter: function (params) {
            return params[0].name + '<br/>' + params[0].value.toFixed(2) + '%'
          },
          backgroundColor: 'rgba(255, 255, 255, 0.9)',
          borderColor: '#eee',
          borderWidth: 1,
          textStyle: { color: '#333' },
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '8%',
          top: '5%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          name: '涨跌幅(%)',
          nameTextStyle: {
            color: '#718096',
            fontSize: 12,
          },
          axisLabel: {
            formatter: '{value}%',
            color: '#718096',
            fontSize: 12,
          },
          axisLine: {
            lineStyle: {
              color: '#eaecef',
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              color: '#eaecef',
              type: 'dashed',
            },
          },
        },
        yAxis: {
          type: 'category',
          data: sortedData.map((item) => item.board_name || item.name),
          axisLabel: {
            fontSize: 12,
            color: '#718096',
          },
          axisLine: {
            lineStyle: {
              color: '#eaecef',
            },
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: '涨跌幅',
            type: 'bar',
            data: sortedData.map((item) => item.change_percent),
            itemStyle: {
              color: function (params) {
                return params.data >= 0 ? riseColor : fallColor
              },
              borderRadius: [0, 4, 4, 0],
            },
            label: {
              show: true,
              position: 'right',
              formatter: '{c}%',
              fontSize: 12,
              color: '#718096',
            },
            barWidth: '60%',
          },
        ],
      }
      conceptPerformanceChart.setOption(conceptPerformanceOption, true)
    }
    updateConceptPerformanceChart(true)

    // PE分布饼图
    const peDistributionChart = echarts.init(document.getElementById('pe-distribution-chart'))
    const peDistributionOption = {
      backgroundColor: 'transparent',
      title: {
        text: '市场PE分布',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 16,
          fontWeight: 'normal',
          color: '#333',
        },
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          // 计算总数
          const total = peDistribution.reduce((sum, item) => sum + item.value, 0)
          const percent = ((params.value / total) * 100).toFixed(1)

          return `${params.seriesName}<br/>
                 ${params.name}: <b>${params.value}</b> 只<br/>
                 占比: <b>${percent}%</b>`
        },
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#eee',
        borderWidth: 1,
        padding: 10,
        textStyle: { color: '#333' },
        extraCssText: 'box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);',
      },
      legend: {
        type: 'scroll',
        orient: 'vertical',
        right: 10,
        top: 'center',
        textStyle: { fontSize: 12 },
        formatter: function (name) {
          // 在图例中显示数量
          const item = peDistribution.find((item) => item.name === name)
          return `${name}: ${item.value}只`
        },
      },
      series: [
        {
          name: '市场PE分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['40%', '50%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 6,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 14,
              fontWeight: 'bold',
              formatter: '{b}: {c}只 ({d}%)',
              backgroundColor: 'rgba(255, 255, 255, 0.7)',
              padding: [4, 8],
              borderRadius: 4,
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.3)',
            },
          },
          labelLine: {
            show: false,
          },
          data: peDistribution.map(function (item, index) {
            return {
              name: item.name,
              value: item.value,
              itemStyle: { color: colorPalette[index % colorPalette.length] },
            }
          }),
        },
      ],
    }
    peDistributionChart.setOption(peDistributionOption)

    // 添加筛选器事件监听
    document.getElementById('volume-trend-filter').addEventListener('change', function (e) {
      updateVolumeTrendChart(e.target.value)
    })

    document.getElementById('industry-performance-filter').addEventListener('change', function (e) {
      updateIndustryPerformanceChart(e.target.value === 'top')
    })

    document.getElementById('concept-performance-filter').addEventListener('change', function (e) {
      updateConceptPerformanceChart(e.target.value === 'top')
    })

    // 市场指数趋势图
    let marketIndicesTrend = JSON.parse('{{ market_indices_trend|safe }}')
    console.log('Initial market indices data:', marketIndicesTrend)
    // 初始化市场指数图表
    const marketIndexController = initMarketIndexChart(marketIndicesTrend)

    // 添加市场指数筛选器事件监听
    document.getElementById('market-index-filter').addEventListener('change', function (e) {
      const dateRangeFilter = document.getElementById('date-range-filter').value

      if (dateRangeFilter === 'custom') {
        // 如果是自定义日期范围，使用当前选择的日期
        const startDate = document.getElementById('start-date').value
        const endDate = document.getElementById('end-date').value

        if (startDate && endDate) {
          // 如果日期已选择，使用这些日期获取新数据
          marketIndexController.fetchData(startDate, endDate)
        } else {
          // 如果日期未选择，使用默认的数据
          marketIndexController.updateChart(e.target.value)
        }
      } else {
        // 如果使用预设日期范围，获取新数据
        const days = parseInt(dateRangeFilter)
        marketIndexController.fetchData(null, null, days)
      }
    })

    // 日期区间选择器事件监听
    document.getElementById('date-range-filter').addEventListener('change', function (e) {
      const value = e.target.value
      const customDateRange = document.getElementById('custom-date-range')
      const today = new Date()
      const endDate = new Date(today)

      if (value === 'custom') {
        // 显示自定义日期范围选择器
        customDateRange.style.display = 'flex'
        // 设置默认日期范围（当前日期往前30天）
        const startDate = new Date(today)
        startDate.setDate(today.getDate() - 30)

        document.getElementById('end-date').valueAsDate = endDate
        document.getElementById('start-date').valueAsDate = startDate
      } else {
        // 隐藏自定义日期范围选择器，但仍然填充日期以便于后续使用
        customDateRange.style.display = 'none'

        // 解析天数并设置日期范围
        const days = parseInt(value)
        const startDate = new Date(today)
        startDate.setDate(today.getDate() - days)

        // 更新日期选择器的值（即使它不可见）
        document.getElementById('end-date').valueAsDate = endDate
        document.getElementById('start-date').valueAsDate = startDate

        // 更新显示的日期范围
        document.getElementById('date-range-display').textContent = `(近${days}天)`

        // 请求新数据并更新图表
        marketIndexController.fetchData(null, null, days)
      }
    })

    // 自定义日期范围应用按钮
    document.getElementById('apply-date-range').addEventListener('click', function () {
      const startDate = document.getElementById('start-date').value
      const endDate = document.getElementById('end-date').value

      if (!startDate || !endDate) {
        alert('请选择完整的日期范围')
        return
      }

      // 验证日期范围
      const startDateObj = new Date(startDate)
      const endDateObj = new Date(endDate)

      if (startDateObj > endDateObj) {
        alert('开始日期不能晚于结束日期')
        return
      }

      // 格式化显示的日期
      const formattedStart = new Date(startDate).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
      const formattedEnd = new Date(endDate).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
      document.getElementById('date-range-display').textContent = `(${formattedStart} - ${formattedEnd})`

      // 请求新数据并更新图表
      marketIndexController.fetchData(startDate, endDate)
    })

    // 窗口大小改变时，重新调整图表大小
    window.addEventListener('resize', function () {
      industryChart.resize()
      volumeTrendChart.resize()
      industryPerformanceChart.resize()
      conceptPerformanceChart.resize()
      peDistributionChart.resize()
      marketIndexController.resize()
    })
  })
</script>
{% endblock %}
