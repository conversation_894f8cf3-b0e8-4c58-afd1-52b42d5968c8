import os

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "dj_stock.settings")
import django

django.setup()
from django.db import connection

# 读取SQL文件
with open("add_field_comments_mysql.sql", "r", encoding="utf-8") as f:
    sql = f.read()

# 执行SQL语句
with connection.cursor() as cursor:
    for statement in sql.split(";"):
        if statement.strip():
            try:
                cursor.execute(statement)
                print(f"执行SQL成功: {statement.strip()[:50]}...")
            except Exception as e:
                print(f"执行SQL失败: {statement.strip()[:50]}... - {str(e)}")

print("数据库字段注释添加完成")
