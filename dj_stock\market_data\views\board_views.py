# -*- coding: utf-8 -*-
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.db.models import Q
from django.core.paginator import Paginator

from .base_views import (
    StockBasic,
    StockIndustryBoard,
    StockIndustryBoardRelation,
    StockBoardConcept,
    StockBoardConceptRelation,
    StockDailyQuote,
    get_pagination,
)


def industry_board_list(request):
    """行业板块列表视图"""
    # 获取查询参数
    search_query = request.GET.get("query", "")
    sort_by = request.GET.get("sort", "-change_percent")
    selected_date = request.GET.get("date", "")

    # 获取可用的日期
    available_dates = list(
        StockIndustryBoard.objects.dates("date", "day", order="DESC")[:30]
    )

    # 如果没有指定日期，则使用最新日期
    if not selected_date and available_dates:
        selected_date = available_dates[0].strftime("%Y-%m-%d")

    # 构建查询
    industry_boards = StockIndustryBoard.objects.all()

    # 应用过滤条件
    if selected_date:
        industry_boards = industry_boards.filter(date=selected_date)

    if search_query:
        industry_boards = industry_boards.filter(
            Q(board_name__icontains=search_query)
            | Q(board_code__icontains=search_query)
        )

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        industry_boards = industry_boards.order_by(f"-{sort_field}")
    else:
        industry_boards = industry_boards.order_by(sort_by)

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(industry_boards, page, per_page=20)

    # 总市值单位转换
    for board in page_obj:
        if board.total_market_value is not None:
            # 转换为亿元
            board.total_market_value = board.total_market_value / 100000000

    context = {
        "page_obj": page_obj,
        "search_query": search_query,
        "available_dates": available_dates,
        "selected_date": selected_date,
        "sort_by": sort_by,
    }

    return render(request, "market_data/industry_board_list.html", context)


def industry_board_detail(request, code):
    """行业板块详情视图"""
    try:
        from datetime import datetime

        # 获取查询参数
        search_query = request.GET.get("query", "")
        sort_by = request.GET.get("sort", "-change_percent")
        selected_date_str = request.GET.get("date", "")

        # 获取可用的日期
        available_dates = list(
            StockIndustryBoard.objects.filter(board_code=code).dates(
                "date", "day", order="DESC"
            )[:30]
        )

        # 如果没有指定日期，则使用最新日期
        if not selected_date_str and available_dates:
            selected_date_str = available_dates[0].strftime("%Y-%m-%d")

        # 如果仍然没有日期，返回错误页面
        if not selected_date_str:
            context = {
                "error_message": f"未找到板块代码 {code} 的数据",
                "board_code": code,
            }
            return render(request, "market_data/industry_board_detail.html", context)

        # 将字符串日期转换为日期对象
        try:
            selected_date = datetime.strptime(selected_date_str, "%Y-%m-%d").date()
        except ValueError:
            context = {
                "error_message": f"日期格式错误: {selected_date_str}",
                "board_code": code,
                "available_dates": available_dates,
            }
            return render(request, "market_data/industry_board_detail.html", context)

        # 获取板块信息
        try:
            board = StockIndustryBoard.objects.get(board_code=code, date=selected_date)
        except StockIndustryBoard.DoesNotExist:
            context = {
                "error_message": f"未找到板块代码 {code} 在日期 {selected_date_str} 的数据",
                "board_code": code,
                "available_dates": available_dates,
                "selected_date": selected_date,
                "selected_date_str": selected_date_str,
            }
            return render(request, "market_data/industry_board_detail.html", context)

        # 总市值单位转换
        if board.total_market_value is not None:
            # 转换为亿元
            board.total_market_value = board.total_market_value / 100000000

        # 获取板块成分股 - 优化查询，只获取需要的字段
        # 如果有指定日期，则按日期过滤；否则获取最新的记录
        if selected_date:
            stocks = StockIndustryBoardRelation.objects.filter(
                board_code=code, date=selected_date
            ).select_related()
        else:
            # 如果没有指定日期，获取最新日期的记录
            # 先获取该板块的最新日期
            latest_relation_date = (
                StockIndustryBoardRelation.objects.filter(board_code=code)
                .order_by("-date")
                .values_list("date", flat=True)
                .first()
            )

            if latest_relation_date:
                stocks = StockIndustryBoardRelation.objects.filter(
                    board_code=code, date=latest_relation_date
                ).select_related()
            else:
                # 如果没有找到任何记录，返回空查询集
                stocks = StockIndustryBoardRelation.objects.none()

        # 如果指定了日期，获取该日期的股票行情数据 - 优化为批量查询
        if selected_date:
            stock_codes = list(stocks.values_list("stock_code", flat=True))
            daily_quotes = {
                quote.stock_code: quote
                for quote in StockDailyQuote.objects.filter(
                    stock_code__in=stock_codes, trade_date=selected_date
                )
            }

            # 为每个股票添加行情数据
            for stock in stocks:
                if stock.stock_code in daily_quotes:
                    daily_data = daily_quotes[stock.stock_code]
                    stock.latest_price = daily_data.close_price
                    stock.change_percent = daily_data.change_percent
                    stock.change_amount = daily_data.change_amount
                    stock.turnover_rate = daily_data.turnover_rate
                    stock.pe_ratio = daily_data.pe_ratio
                    stock.pb_ratio = daily_data.pb_ratio
                    stock.volume = daily_data.volume
                    stock.amount = daily_data.amount
                else:
                    # 如果没有行情数据，设置默认值
                    stock.latest_price = None
                    stock.change_percent = None
                    stock.change_amount = None
                    stock.turnover_rate = None
                    stock.pe_ratio = None
                    stock.pb_ratio = None
                    stock.volume = None
                    stock.amount = None

        # 应用搜索过滤
        if search_query:
            stocks = stocks.filter(
                Q(stock_name__icontains=search_query)
                | Q(stock_code__icontains=search_query)
            )

        # 获取总数量（在排序和分页之前）
        total_stocks = stocks.count()

        # 应用排序
        if sort_by.startswith("-"):
            sort_field = sort_by[1:]
            stocks = stocks.order_by(f"-{sort_field}")
        else:
            stocks = stocks.order_by(sort_by)

        # 分页
        page = request.GET.get("page", 1)
        page_obj = get_pagination(stocks, page, per_page=20)

        # 成交量和成交额单位转换
        for stock in page_obj:
            if hasattr(stock, "volume") and stock.volume:
                stock.volume = float(stock.volume) / 10000  # 转为万手
            if hasattr(stock, "amount") and stock.amount:
                stock.amount = float(stock.amount) / 100000000  # 转为亿元

        context = {
            "board": board,
            "page_obj": page_obj,
            "search_query": search_query,
            "available_dates": available_dates,
            "selected_date": selected_date,
            "selected_date_str": selected_date_str,
            "sort_by": sort_by,
            "total_stocks": total_stocks,
            "total_items": total_stocks,  # 添加模板需要的变量
        }

        return render(request, "market_data/industry_board_detail.html", context)

    except Exception as e:
        # 记录错误日志
        import logging

        logger = logging.getLogger(__name__)
        logger.error(f"Error in industry_board_detail for code {code}: {str(e)}")

        context = {
            "error_message": f"加载板块详情时发生错误: {str(e)}",
            "board_code": code,
        }
        return render(request, "market_data/industry_board_detail.html", context)


def industry_chain_list(request):
    """产业链列表视图"""
    # 获取查询参数
    search_query = request.GET.get("search", "")
    sort_by = request.GET.get("sort", "-change_percent")
    date = request.GET.get("date", "")

    # 暂时复用行业板块数据
    # 获取可用的日期
    available_dates = list(
        StockIndustryBoard.objects.dates("date", "day", order="DESC")[:30]
    )

    # 如果没有指定日期，则使用最新日期
    if not date and available_dates:
        date = available_dates[0].strftime("%Y-%m-%d")

    # 构建查询
    industry_boards = StockIndustryBoard.objects.all()

    # 应用过滤条件
    if date:
        industry_boards = industry_boards.filter(date=date)

    if search_query:
        industry_boards = industry_boards.filter(
            Q(board_name__icontains=search_query)
            | Q(board_code__icontains=search_query)
        )

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        industry_boards = industry_boards.order_by(f"-{sort_field}")
    else:
        industry_boards = industry_boards.order_by(sort_by)

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(industry_boards, page, per_page=20)

    context = {
        "page_obj": page_obj,
        "total_items": industry_boards.count(),
        "search_query": search_query,
        "available_dates": available_dates,
        "selected_date": date,
        "sort_by": sort_by,
    }

    return render(request, "market_data/industry_chain_list.html", context)


def industry_chain(request):
    """产业链关系视图"""
    # 获取查询参数
    search_query = request.GET.get("search", "")
    industry = request.GET.get("industry", "")
    relation_type = request.GET.get("relation_type", "")

    # 暂时使用行业板块数据作为示例
    # 获取所有行业名称
    industries = StockIndustryBoard.objects.values_list(
        "board_name", flat=True
    ).distinct()

    # 构建基础查询，使用行业板块关系表
    relations = StockIndustryBoardRelation.objects.all()

    # 应用过滤条件
    if search_query:
        relations = relations.filter(
            Q(stock_name__icontains=search_query)
            | Q(stock_code__icontains=search_query)
        )

    if industry:
        relations = relations.filter(board_name=industry)

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(relations, page, per_page=20)

    # 为了临时展示，将行业板块数据转换为产业链关系格式
    for item in page_obj:
        item.related_stock_code = f"{item.stock_code[:3]}***"  # 模拟关联股票代码
        item.related_stock_name = f"{item.stock_name}相关企业"  # 模拟关联股票名称
        item.relation_type = relation_type or (
            "上游" if item.rank % 3 == 0 else "下游" if item.rank % 3 == 1 else "同业"
        )
        item.relation_weight = round(0.5 + (item.rank % 10) / 10, 2)  # 模拟关联度权重
        item.industry_name = item.board_name  # 使用板块名称作为产业链名称

    context = {
        "page_obj": page_obj,
        "total_items": relations.count(),
        "search_query": search_query,
        "selected_industry": industry,
        "relation_type": relation_type,
        "industries": industries,
    }

    return render(request, "market_data/industry_chain.html", context)


def board_concept(request):
    """概念板块列表视图"""
    # 获取查询参数
    search_query = request.GET.get("query", "")
    sort_by = request.GET.get("sort", "-change_percent")
    selected_date = request.GET.get("date", "")

    # 获取可用的日期
    available_dates = list(
        StockBoardConcept.objects.dates("date", "day", order="DESC")[:30]
    )

    # 如果没有指定日期，则使用最新日期
    if not selected_date and available_dates:
        selected_date = available_dates[0].strftime("%Y-%m-%d")

    # 构建查询
    concept_boards = StockBoardConcept.objects.all()

    # 应用过滤条件
    if selected_date:
        concept_boards = concept_boards.filter(date=selected_date)

    if search_query:
        concept_boards = concept_boards.filter(
            Q(board_name__icontains=search_query)
            | Q(board_code__icontains=search_query)
        )

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        concept_boards = concept_boards.order_by(f"-{sort_field}")
    else:
        concept_boards = concept_boards.order_by(sort_by)

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(concept_boards, page, per_page=20)

    # 总市值单位转换
    for board in page_obj:
        if board.total_market_value is not None:
            # 转换为亿元
            board.total_market_value = board.total_market_value / 100000000

    context = {
        "page_obj": page_obj,
        "search_query": search_query,
        "available_dates": available_dates,
        "selected_date": selected_date,
        "sort_by": sort_by,
    }

    return render(request, "market_data/board_concept.html", context)


def board_concept_detail(request, board_code):
    """概念板块详情视图"""
    # 获取查询参数
    search_query = request.GET.get("query", "")
    sort_by = request.GET.get("sort", "-change_percent")
    date = request.GET.get("date", "")

    # 获取可用的日期
    available_dates = list(
        StockBoardConcept.objects.filter(board_code=board_code).dates(
            "date", "day", order="DESC"
        )[:30]
    )

    # 如果没有指定日期，则使用最新日期
    if not date and available_dates:
        date = available_dates[0].strftime("%Y-%m-%d")

    # 获取板块信息
    board = get_object_or_404(StockBoardConcept, board_code=board_code, date=date)

    # 总市值单位转换
    if board.total_market_value is not None:
        # 转换为亿元
        board.total_market_value = board.total_market_value / 100000000

    # 获取板块成分股
    relations = StockBoardConceptRelation.objects.filter(
        board_code=board_code, date=date
    )

    # 应用搜索过滤
    if search_query:
        relations = relations.filter(
            Q(stock_name__icontains=search_query)
            | Q(stock_code__icontains=search_query)
        )

    # 应用排序
    if sort_by.startswith("-"):
        sort_field = sort_by[1:]
        relations = relations.order_by(f"-{sort_field}")
    else:
        relations = relations.order_by(sort_by)

    # 处理成交量和成交额单位
    for relation in relations:
        if relation.volume:
            relation.volume = float(relation.volume) / 10000  # 转为万手
        if relation.amount:
            relation.amount = float(relation.amount) / 100000000  # 转为亿元

    # 分页
    page = request.GET.get("page", 1)
    page_obj = get_pagination(relations, page, per_page=20)

    context = {
        "board": board,
        "page_obj": page_obj,
        "search_query": search_query,
        "available_dates": available_dates,
        "selected_date": date,
        "sort_by": sort_by,
        "total_stocks": relations.count(),
    }

    return render(request, "market_data/board_concept_detail.html", context)


def industry_stocks(request, industry_name):
    """按行业筛选股票的视图"""
    # 获取查询参数
    sort_by = request.GET.get("sort", "-change_percent")
    page = request.GET.get("page", 1)

    # 获取可用的日期
    available_dates = list(
        StockDailyQuote.objects.dates("trade_date", "day", order="DESC")[:30]
    )
    latest_date = available_dates[0] if available_dates else None

    # 获取行业内的股票
    stocks = StockBasic.objects.filter(industry=industry_name)

    # 获取最新行情数据
    if latest_date:
        for stock in stocks:
            try:
                quote = StockDailyQuote.objects.get(
                    stock_code=stock.stock_code, trade_date=latest_date
                )
                stock.latest_price = quote.close_price
                stock.change_percent = quote.change_percent
                stock.change_amount = quote.change_amount
                stock.turnover_rate = quote.turnover_rate
                stock.volume = (
                    float(quote.volume) / 10000 if quote.volume else 0
                )  # 转为万手
                stock.amount = (
                    float(quote.amount) / 100000000 if quote.amount else 0
                )  # 转为亿元
                stock.pe_ratio = quote.pe_ratio
                stock.market_value = (
                    float(quote.total_value) / 100000000 if quote.total_value else 0
                )  # 转为亿元
            except StockDailyQuote.DoesNotExist:
                pass

    # 应用排序
    # 由于我们为StockBasic对象添加了动态属性，直接排序可能会出问题
    # 这里采用简单的方式：先转换为列表，然后基于属性排序
    stocks = list(stocks)

    if sort_by == "latest_price":
        stocks.sort(key=lambda x: getattr(x, "latest_price", 0) or 0)
    elif sort_by == "-latest_price":
        stocks.sort(key=lambda x: getattr(x, "latest_price", 0) or 0, reverse=True)
    elif sort_by == "change_percent":
        stocks.sort(key=lambda x: getattr(x, "change_percent", 0) or 0)
    elif sort_by == "-change_percent":
        stocks.sort(key=lambda x: getattr(x, "change_percent", 0) or 0, reverse=True)
    elif sort_by == "turnover_rate":
        stocks.sort(key=lambda x: getattr(x, "turnover_rate", 0) or 0)
    elif sort_by == "-turnover_rate":
        stocks.sort(key=lambda x: getattr(x, "turnover_rate", 0) or 0, reverse=True)
    elif sort_by == "market_value":
        stocks.sort(key=lambda x: getattr(x, "market_value", 0) or 0)
    elif sort_by == "-market_value":
        stocks.sort(key=lambda x: getattr(x, "market_value", 0) or 0, reverse=True)
    else:
        # 默认按股票代码排序
        stocks.sort(key=lambda x: x.stock_code)

    # 分页
    paginator = Paginator(stocks, 20)
    try:
        page_obj = paginator.page(page)
    except:
        page_obj = paginator.page(1)

    context = {
        "industry_name": industry_name,
        "page_obj": page_obj,
        "total_stocks": len(stocks),
        "sort_by": sort_by,
        "latest_date": latest_date,
    }

    return render(request, "market_data/industry_stocks.html", context)
