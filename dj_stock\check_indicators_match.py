import os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dj_stock.settings')
import django
django.setup()
import akshare as ak
import pandas as pd

# 获取财务摘要数据
df = ak.stock_financial_abstract(symbol='600000')

# 获取所有指标名称
indicators = df['指标'].tolist()

# 从financial_fetcher.py中获取指标映射字典
from financial_analysis.utils.financial_fetcher import FinancialDataFetcher
fetcher = FinancialDataFetcher()

# 获取指标映射字典
indicator_mapping = {}
for line in open('financial_analysis/utils/financial_fetcher.py', 'r', encoding='utf-8'):
    if '"' in line and ':' in line and 'indicator_mapping' in line:
        parts = line.split('"')
        if len(parts) >= 3:
            key = parts[1]
            value_part = parts[2].split('"')[1] if len(parts[2].split('"')) > 1 else None
            if value_part:
                indicator_mapping[key] = value_part

# 检查哪些指标在API返回中存在但在映射字典中不存在
missing_indicators = [ind for ind in indicators if ind not in indicator_mapping]
print("API返回的指标但在映射字典中不存在:")
for ind in missing_indicators:
    print(f"  - {ind}")

# 检查哪些指标在映射字典中存在但在API返回中不存在
extra_indicators = [ind for ind in indicator_mapping if ind not in indicators]
print("\n映射字典中存在但在API返回中不存在的指标:")
for ind in extra_indicators:
    print(f"  - {ind}")

# 打印匹配的指标
matching_indicators = [ind for ind in indicators if ind in indicator_mapping]
print(f"\n匹配的指标数量: {len(matching_indicators)}/{len(indicators)}")
print("匹配的指标:")
for ind in matching_indicators:
    print(f"  - {ind} -> {indicator_mapping[ind]}")
