#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
股票数据刷新工具

该脚本查询前100个股票代码，并使用requests库循环访问指定地址来刷新股票的历史记录和指标等数据。
"""

import os
import sys
import time
import random
import requests
import django
from tqdm import tqdm

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "dj_stock.settings")
django.setup()

# 导入Django模型
from market_data.models import StockBasic


def refresh_stock_data(start=0, end=100):
    """
    刷新股票数据

    """
    # 获取前N个股票代码
    stocks = StockBasic.objects.all().order_by("stock_code")[start:end]

    # 显示进度条
    with tqdm(total=len(stocks), desc="刷新股票数据") as pbar:
        for stock in stocks:
            stock_code = stock.stock_code
            stock_name = stock.stock_name

            # 构建URL
            url = f"http://127.0.0.1:8000/stocks/{stock_code}/"

            try:
                # 发送请求
                response = requests.get(url, timeout=30)

                # 检查响应状态
                if response.status_code == 200:
                    pbar.write(f"成功刷新 {stock_name}({stock_code}) 的数据")
                else:
                    pbar.write(
                        f"刷新 {stock_name}({stock_code}) 失败，状态码: {response.status_code}"
                    )

            except requests.RequestException as e:
                pbar.write(f"请求 {stock_name}({stock_code}) 时出错: {str(e)}")

            # 更新进度条
            pbar.update(1)

            # 随机延迟，避免请求过于频繁
            time.sleep(random.uniform(8, 15))


if __name__ == "__main__":
    # 解析命令行参数
    start = 5001  # 开始的股票索引，从0开始计数，表示从第一个股票开始刷新。
    end = 5500  # 结束的股票索引，表示刷新到第100个股票。
    print(f"开始刷新第 {start} -- {end} 个股票的数据...")
    refresh_stock_data(start, end)
    print("刷新完成！")
