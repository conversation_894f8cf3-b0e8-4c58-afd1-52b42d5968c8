# -*- coding: utf-8 -*-
from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Count, Sum, Q
from datetime import datetime, timedelta
import json

from .base_views import (
    StockBasic,
    StockDailyQuote,
    StockRiskWarning,
    StockIndustryBoard,
    StockMarketIndex,
)


def data_statistics(request):
    """数据统计视图"""
    # 检查是否是AJAX请求
    is_ajax = (
        request.headers.get("X-Requested-With") == "XMLHttpRequest"
        or request.GET.get("format") == "json"
    )

    # 添加调试日志
    import logging

    logger = logging.getLogger(__name__)
    logger.info(f"Request received. Method: {request.method}, Path: {request.path}")
    logger.info(f"Headers: {dict(request.headers.items())}")
    logger.info(f"GET params: {dict(request.GET.items())}")
    logger.info(f"is_ajax: {is_ajax}")
    # 获取行业分布统计
    industry_stats = (
        StockBasic.objects.values("industry")
        .annotate(count=Count("industry"))
        .filter(count__gt=0)
        .order_by("-count")
    )
    industry_stats_json = json.dumps(list(industry_stats))

    # 计算行业数量
    industry_count = industry_stats.count()

    # 获取当前日期和前一交易日
    current_date = datetime.now().date()

    # 获取最新交易日的数据
    latest_trade_date = (
        StockDailyQuote.objects.order_by("-trade_date").values("trade_date").first()
    )
    if latest_trade_date:
        latest_date = latest_trade_date["trade_date"]
        # 获取前一交易日
        prev_trade_date = (
            StockDailyQuote.objects.filter(trade_date__lt=latest_date)
            .order_by("-trade_date")
            .values("trade_date")
            .first()
        )
        if prev_trade_date:
            prev_date = prev_trade_date["trade_date"]
        else:
            prev_date = latest_date - timedelta(days=1)
    else:
        latest_date = current_date
        prev_date = current_date - timedelta(days=1)

    # 1. 市场平均市盈率 (PE)
    # 获取有效的PE数据（排除负值和异常值）
    valid_pe_stocks = StockDailyQuote.objects.filter(
        trade_date=latest_date, pe_ratio__gt=0, pe_ratio__lt=200  # 排除异常值
    ).values_list("pe_ratio", flat=True)

    if valid_pe_stocks:
        avg_pe = round(sum(valid_pe_stocks) / len(valid_pe_stocks), 2)
    else:
        avg_pe = 0

    # 获取前一交易日的平均PE
    prev_valid_pe_stocks = StockDailyQuote.objects.filter(
        trade_date=prev_date, pe_ratio__gt=0, pe_ratio__lt=200
    ).values_list("pe_ratio", flat=True)

    if prev_valid_pe_stocks and len(prev_valid_pe_stocks) > 0:
        prev_avg_pe = sum(prev_valid_pe_stocks) / len(prev_valid_pe_stocks)
        pe_change = round(((avg_pe - prev_avg_pe) / prev_avg_pe) * 100, 2)
    else:
        pe_change = 0

    # 2. 市场平均市净率 (PB)
    valid_pb_stocks = StockDailyQuote.objects.filter(
        trade_date=latest_date, pb_ratio__gt=0, pb_ratio__lt=20  # 排除异常值
    ).values_list("pb_ratio", flat=True)

    if valid_pb_stocks:
        avg_pb = round(sum(valid_pb_stocks) / len(valid_pb_stocks), 2)
    else:
        avg_pb = 0

    # 获取前一交易日的平均PB
    prev_valid_pb_stocks = StockDailyQuote.objects.filter(
        trade_date=prev_date, pb_ratio__gt=0, pb_ratio__lt=20
    ).values_list("pb_ratio", flat=True)

    if prev_valid_pb_stocks and len(prev_valid_pb_stocks) > 0:
        prev_avg_pb = sum(prev_valid_pb_stocks) / len(prev_valid_pb_stocks)
        pb_change = round(((avg_pb - prev_avg_pb) / prev_avg_pb) * 100, 2)
    else:
        pb_change = 0

    # 3. 上涨/下跌股票比例
    up_stocks = StockDailyQuote.objects.filter(
        trade_date=latest_date, change_percent__gt=0
    ).count()

    down_stocks = StockDailyQuote.objects.filter(
        trade_date=latest_date, change_percent__lt=0
    ).count()

    flat_stocks = StockDailyQuote.objects.filter(
        trade_date=latest_date, change_percent=0
    ).count()

    total_active_stocks = up_stocks + down_stocks + flat_stocks

    if total_active_stocks > 0:
        up_down_ratio = (
            round(up_stocks / down_stocks, 2) if down_stocks > 0 else up_stocks
        )
        up_percent = round((up_stocks / total_active_stocks) * 100, 2)
        down_percent = round((down_stocks / total_active_stocks) * 100, 2)
    else:
        up_down_ratio = 0
        up_percent = 0
        down_percent = 0

    # 上一交易日的上涨/下跌比例
    prev_up_stocks = StockDailyQuote.objects.filter(
        trade_date=prev_date, change_percent__gt=0
    ).count()

    prev_down_stocks = StockDailyQuote.objects.filter(
        trade_date=prev_date, change_percent__lt=0
    ).count()

    if prev_down_stocks > 0:
        prev_up_down_ratio = prev_up_stocks / prev_down_stocks
        ratio_change = round(
            ((up_down_ratio - prev_up_down_ratio) / prev_up_down_ratio) * 100, 2
        )
    else:
        ratio_change = 0

    # 4. 成交量变化率
    current_volume = (
        StockDailyQuote.objects.filter(trade_date=latest_date).aggregate(
            total_volume=Sum("volume")
        )["total_volume"]
        or 0
    )

    prev_volume = (
        StockDailyQuote.objects.filter(trade_date=prev_date).aggregate(
            total_volume=Sum("volume")
        )["total_volume"]
        or 0
    )

    if prev_volume > 0:
        volume_change = round(((current_volume - prev_volume) / prev_volume) * 100, 2)
    else:
        volume_change = 0

    # 获取市场成交量趋势数据（最近30天）
    volume_trend = (
        StockDailyQuote.objects.filter(
            trade_date__gte=current_date - timedelta(days=30)
        )
        .values("trade_date")
        .annotate(total_volume=Sum("volume"), total_amount=Sum("amount"))
        .order_by("trade_date")
    )

    # 转换数据格式，处理日期和单位
    volume_trend_data = []
    for item in volume_trend:
        volume_trend_data.append(
            {
                "date": item["trade_date"].strftime("%Y-%m-%d"),
                "volume": (
                    float(item["total_volume"]) / 100000000
                    if item["total_volume"]
                    else 0
                ),  # 转为亿手
                "amount": (
                    float(item["total_amount"]) / 100000000
                    if item["total_amount"]
                    else 0
                ),  # 转为亿元
            }
        )

    volume_trend_json = json.dumps(volume_trend_data)

    # 行业板块涨跌幅分布
    industry_boards = StockIndustryBoard.objects.all().order_by("-change_percent")

    # 转换行业板块数据为JSON格式
    industry_performance_data = []
    for board in industry_boards:
        industry_performance_data.append(
            {
                "name": board.board_name,
                "board_name": board.board_name,  # 添加board_name字段，用于图表显示
                "code": board.board_code,
                "change_percent": (
                    float(board.change_percent) if board.change_percent else 0
                ),
                "turnover_rate": (
                    float(board.turnover_rate) if board.turnover_rate else 0
                ),
            }
        )

    industry_performance_json = json.dumps(industry_performance_data)

    # 概念板块涨跌幅分布（假设与行业板块类似）
    # 这里使用行业板块数据作为示例，实际应该使用概念板块数据
    concept_performance_json = json.dumps(
        industry_performance_data[:15]
    )  # 取前15个作为示例

    # PE分布数据 - 使用真实数据
    # 获取最新交易日的PE分布
    pe_ranges = [
        {"min": 0, "max": 15, "name": "0-15 (低估值)"},
        {"min": 15, "max": 30, "name": "15-30 (合理估值)"},
        {"min": 30, "max": 50, "name": "30-50 (偏高估值)"},
        {"min": 50, "max": 100, "name": "50-100 (高估值)"},
        {"min": 100, "max": 10000, "name": "100+ (超高估值)"},
    ]

    pe_distribution_data = []

    # 计算负PE的股票数量
    negative_pe_count = StockDailyQuote.objects.filter(
        trade_date=latest_date, pe_ratio__lt=0
    ).count()

    pe_distribution_data.append({"name": "负PE", "value": negative_pe_count})

    # 计算各个PE区间的股票数量
    for pe_range in pe_ranges:
        count = StockDailyQuote.objects.filter(
            trade_date=latest_date,
            pe_ratio__gte=pe_range["min"],
            pe_ratio__lt=pe_range["max"],
        ).count()

        pe_distribution_data.append({"name": pe_range["name"], "value": count})

    pe_distribution_json = json.dumps(pe_distribution_data)

    # 获取最新交易日期
    latest_date = (
        StockDailyQuote.objects.order_by("-trade_date")
        .values_list("trade_date", flat=True)
        .first()
    )

    # 市场指数趋势
    market_indices_trend = {}
    main_indices = [
        "000001",  # 上证指数
        "399001",  # 深证成指
        "000300",  # 沪深300
    ]

    # 默认获取过去30天的数据，但也支持自定义日期范围
    days_range = request.GET.get("days", "30")
    start_date_str = request.GET.get("start_date", None)
    end_date_str = request.GET.get("end_date", None)

    # 获取指定的指数代码，如果没有指定，则使用默认的指数列表
    specific_index_code = request.GET.get("index_code", None)

    # 如果提供了自定义日期范围
    if start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            date_filter = Q(trade_date__gte=start_date) & Q(trade_date__lte=end_date)
        except ValueError:
            # 如果日期格式不正确，回退到默认值
            date_filter = Q(
                trade_date__gte=current_date - timedelta(days=int(days_range))
            )
    else:
        # 使用天数范围
        try:
            days = int(days_range)
            date_filter = Q(trade_date__gte=current_date - timedelta(days=days))
        except ValueError:
            # 如果天数不是有效整数，使用默认30天
            date_filter = Q(trade_date__gte=current_date - timedelta(days=30))

    # 如果指定了特定的指数代码，则只获取该指数的数据
    indices_to_process = [specific_index_code] if specific_index_code else main_indices

    for index_code in indices_to_process:
        # 获取指定日期范围的指数数据
        index_data = (
            StockMarketIndex.objects.filter(Q(index_code=index_code) & date_filter)
            .order_by("trade_date")
            .values("trade_date", "close_price", "index_name")
        )

        # 获取指数名称
        index_name = ""
        if index_data:
            index_name = index_data[0]["index_name"]

        # 转换数据格式
        trend_data = []
        prev_close = None

        for i, item in enumerate(index_data):
            current_close = float(item["close_price"]) if item["close_price"] else 0

            # 如果不是第一个数据点，使用前一个数据点的收盘价作为前一交易日收盘价
            if i > 0:
                prev_close = (
                    float(index_data[i - 1]["close_price"])
                    if index_data[i - 1]["close_price"]
                    else current_close
                )
            else:
                # 对于第一个数据点，使用当前值作为前一交易日收盘价
                prev_close = current_close

            trend_data.append(
                {
                    "date": item["trade_date"].strftime("%Y-%m-%d"),
                    "value": current_close,
                    "prevClose": prev_close,
                }
            )

        market_indices_trend[index_code] = {"name": index_name, "data": trend_data}

    market_indices_trend_json = json.dumps(market_indices_trend)

    # 如果是AJAX请求，只返回市场指数数据
    if is_ajax:
        # 添加调试日志
        import logging

        logger = logging.getLogger(__name__)
        logger.info(f"AJAX request detected. Headers: {dict(request.headers.items())}")
        logger.info(f"Query params: {dict(request.GET.items())}")

        # 检查是否请求特定的数据类型
        data_type = request.GET.get("data_type", "market_indices")

        if data_type == "market_indices":
            # 返回市场指数数据
            # 使用已经序列化的JSON字符串
            logger.info(
                f"Returning JSON response with data: {market_indices_trend_json[:100]}..."
            )

            from django.http import HttpResponse

            return HttpResponse(
                market_indices_trend_json, content_type="application/json"
            )

    # 对于非AJAX请求，返回完整的HTML页面
    context = {
        "industry_stats": industry_stats_json,
        "industry_count": industry_count,
        "volume_trend": volume_trend_json,
        "industry_boards": industry_boards[:10],  # 只取前10个行业板块
        "latest_date": latest_date,
        "market_indices_trend": market_indices_trend_json,
        "industry_performance": industry_performance_json,
        "concept_performance": concept_performance_json,
        "pe_distribution": pe_distribution_json,
        # 新的市场指标
        "avg_pe": avg_pe,
        "pe_change": pe_change,
        "avg_pb": avg_pb,
        "pb_change": pb_change,
        "up_stocks": up_stocks,
        "down_stocks": down_stocks,
        "flat_stocks": flat_stocks,
        "up_percent": up_percent,
        "down_percent": down_percent,
        "up_down_ratio": up_down_ratio,
        "ratio_change": ratio_change,
        "volume_change": volume_change,
        "total_active_stocks": total_active_stocks,
    }

    return render(request, "market_data/data_statistics.html", context)


def data_quality(request):
    """数据质量监控视图"""
    # 获取最新交易日期
    latest_date = (
        StockDailyQuote.objects.order_by("-trade_date")
        .values_list("trade_date", flat=True)
        .first()
    )

    # 计算数据完整性指标
    stocks_count = StockBasic.objects.count()
    quotes_count = (
        StockDailyQuote.objects.filter(trade_date=latest_date).count()
        if latest_date
        else 0
    )
    data_coverage = (
        round((quotes_count / stocks_count * 100), 2) if stocks_count > 0 else 0
    )

    # 获取最近更新的数据统计
    current_date = datetime.now().date()
    recent_updates = {
        "daily_quotes": StockDailyQuote.objects.filter(
            update_time__date=current_date
        ).count(),
        "market_indices": StockMarketIndex.objects.filter(
            update_time__date=current_date
        ).count(),
        "risk_warnings": StockRiskWarning.objects.filter(
            update_time__date=current_date
        ).count(),
    }

    # 计算数据更新时间情况
    data_timestamps = {
        "daily_quotes": StockDailyQuote.objects.order_by("-update_time")
        .values_list("update_time", flat=True)
        .first(),
        "market_indices": StockMarketIndex.objects.order_by("-update_time")
        .values_list("update_time", flat=True)
        .first(),
        "risk_warnings": StockRiskWarning.objects.order_by("-update_time")
        .values_list("update_time", flat=True)
        .first(),
    }

    context = {
        "latest_date": latest_date,
        "stocks_count": stocks_count,
        "quotes_count": quotes_count,
        "data_coverage": data_coverage,
        "recent_updates": recent_updates,
        "data_timestamps": data_timestamps,
    }

    return render(request, "market_data/data_quality.html", context)


def data_source(request):
    """数据源信息视图"""
    # 这里可以展示数据源的信息、更新频率、使用说明等

    data_sources = [
        {
            "name": "行情数据",
            "provider": "Tushare",
            "update_frequency": "每日更新",
            "last_update": StockDailyQuote.objects.order_by("-update_time")
            .values_list("update_time", flat=True)
            .first(),
            "description": "股票日线行情数据，包括开盘价、收盘价、最高价、最低价、成交量、成交额等",
        },
        {
            "name": "指数数据",
            "provider": "Tushare",
            "update_frequency": "每日更新",
            "last_update": StockMarketIndex.objects.order_by("-update_time")
            .values_list("update_time", flat=True)
            .first(),
            "description": "市场指数数据，包括上证指数、深证成指、创业板指等",
        },
        {
            "name": "风险预警",
            "provider": "交易所公告",
            "update_frequency": "实时更新",
            "last_update": StockRiskWarning.objects.order_by("-update_time")
            .values_list("update_time", flat=True)
            .first(),
            "description": "股票风险预警信息，包括ST、*ST、退市风险警示等",
        },
    ]

    context = {
        "data_sources": data_sources,
    }

    return render(request, "market_data/data_source.html", context)


def market_indices_api(request):
    """市场指数数据 API接口"""
    import logging
    from django.http import JsonResponse
    from datetime import datetime, timedelta
    from django.db.models import Q

    logger = logging.getLogger(__name__)
    logger.info(f"API request received. Method: {request.method}, Path: {request.path}")
    logger.info(f"Headers: {dict(request.headers.items())}")
    logger.info(f"GET params: {dict(request.GET.items())}")

    # 获取当前日期
    current_date = datetime.now().date()

    # 获取查询参数
    days_range = request.GET.get("days", "30")
    start_date_str = request.GET.get("start_date", None)
    end_date_str = request.GET.get("end_date", None)
    index_code = request.GET.get("index_code", "000001")

    # 如果提供了自定义日期范围
    if start_date_str and end_date_str:
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            date_filter = Q(trade_date__gte=start_date) & Q(trade_date__lte=end_date)
        except ValueError:
            # 如果日期格式不正确，回退到默认值
            date_filter = Q(
                trade_date__gte=current_date - timedelta(days=int(days_range))
            )
    else:
        # 使用天数范围
        try:
            days = int(days_range)
            date_filter = Q(trade_date__gte=current_date - timedelta(days=days))
        except ValueError:
            # 如果天数不是有效整数，使用默认30天
            date_filter = Q(trade_date__gte=current_date - timedelta(days=30))

    # 获取指定日期范围的指数数据
    index_data = (
        StockMarketIndex.objects.filter(Q(index_code=index_code) & date_filter)
        .order_by("trade_date")
        .values("trade_date", "close_price", "index_name")
    )

    # 检查是否有数据
    if not index_data:
        logger.warning(
            f"No data found for index {index_code} in the specified date range"
        )
        # 尝试获取最近的数据
        latest_data = (
            StockMarketIndex.objects.filter(index_code=index_code)
            .order_by("-trade_date")
            .first()
        )
        if latest_data:
            index_name = latest_data.index_name
            logger.info(f"Using index name from latest data: {index_name}")
            # 返回空数据集但带有指数名称
            return JsonResponse(
                {index_code: {"name": index_name, "data": []}}, safe=False
            )
        else:
            # 完全没有数据
            logger.error(f"No data found for index {index_code} at all")
            return JsonResponse(
                {"error": f"No data found for index {index_code}"}, status=404
            )

    # 获取指数名称
    index_name = index_data[0]["index_name"]

    # 转换数据格式
    trend_data = []
    prev_close = None

    for i, item in enumerate(index_data):
        current_close = float(item["close_price"]) if item["close_price"] else 0

        # 如果不是第一个数据点，使用前一个数据点的收盘价作为前一交易日收盘价
        if i > 0:
            prev_close = (
                float(index_data[i - 1]["close_price"])
                if index_data[i - 1]["close_price"]
                else current_close
            )
        else:
            # 对于第一个数据点，使用当前值作为前一交易日收盘价
            prev_close = current_close

        trend_data.append(
            {
                "date": item["trade_date"].strftime("%Y-%m-%d"),
                "value": current_close,
                "prevClose": prev_close,
            }
        )

    # 构建响应数据
    response_data = {
        index_code: {
            "name": index_name,
            "data": trend_data,
        }
    }

    # 添加调试日志
    logger.info(f"Response data structure: {response_data.keys()}")
    logger.info(f"Response data for {index_code}: {response_data[index_code]['name']}")
    logger.info(f"Data points count: {len(trend_data)}")

    if trend_data:
        logger.info(f"First data point: {trend_data[0]}")
        if len(trend_data) > 1:
            logger.info(f"Second data point: {trend_data[1]}")
    else:
        logger.warning("No data points in trend_data")

    # 返回JSON响应
    return JsonResponse(response_data, safe=False)
