﻿{% extends "base.html" %} {% block title %} {% if board %}{{ board.board_name }} | 行业板块详情 {% else %}行业板块详情 {% endif %} | 股票数据分析系统
 {% endblock %} {% block content %}
<!-- 错误信息显示 -->
{% if error_message %}
<div class="alert alert-warning" role="alert">
  <h4 class="alert-heading">数据加载失败</h4>
  <p>{{ error_message }}</p>
  {% if board_code %}
  <p class="mb-0">板块代码: <strong>{{ board_code }}</strong></p>
  {% endif %}
  <hr />
  <p class="mb-0">
    <a href="{% url 'market_data:industry_board_list' %}" class="btn btn-primary"> <i class="bi bi-arrow-left"></i> 返回板块列表 </a>
  </p>
</div>
{% else %}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
  <h1 class="h2">{{ board.board_name }} <small class="text-muted">({{ board.board_code }})</small></h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    {% if available_dates %}
    <div class="dropdown me-2">
      <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
        {% if selected_date_str %}{{ selected_date_str }}{% else %}选择日期{% endif %}
      </button>
      <ul class="dropdown-menu">
        {% for date in available_dates %}
        <li>
          <a
            class="dropdown-item {% if date|date:'Y-m-d' == selected_date_str %}active{% endif %}"
            href="?date={{ date|date:'Y-m-d' }}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
          >
            {{ date|date:"Y-m-d" }}
          </a>
        </li>
        {% endfor %}
      </ul>
    </div>
    {% endif %}
    <a href="{% url 'market_data:industry_board_list' %}" class="btn btn-sm btn-outline-secondary"> <i class="bi bi-arrow-left"></i> 返回板块列表 </a>
  </div>
</div>

<!-- 板块概览卡片 -->
<div class="row mb-4">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">板块概览</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-3 mb-3">
            <div class="card">
              <div class="card-body text-center">
                <h6 class="card-title text-muted">最新价</h6>
                <h3 class="mb-0">{{ board.latest_price|floatformat:2 }}</h3>
                <p class="mb-0 {% if board.change_percent > 0 %}text-danger{% elif board.change_percent < 0 %}text-success{% endif %}">
                  {{ board.change_amount|floatformat:2 }} ({{ board.change_percent|floatformat:2 }}%)
                </p>
              </div>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="card">
              <div class="card-body text-center">
                <h6 class="card-title text-muted">总市值</h6>
                <h3 class="mb-0">{{ board.total_market_value|floatformat:2 }}亿</h3>
                <p class="mb-0 text-muted">换手率: {{ board.turnover_rate|floatformat:2 }}%</p>
              </div>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="card">
              <div class="card-body text-center">
                <h6 class="card-title text-muted">排名</h6>
                <h3 class="mb-0">{{ board.rank|default:"--" }}</h3>
                <p class="mb-0 text-muted">行业板块排名</p>
              </div>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <div class="card">
              <div class="card-body text-center">
                <h6 class="card-title text-muted">涨跌情况</h6>
                <h3 class="mb-0">
                  <span class="text-danger">{{ board.up_count }}</span> /
                  <span class="text-success">{{ board.down_count }}</span>
                </h3>
                <p class="mb-0 text-muted">上涨/下跌家数</p>
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-3">
          <div class="col-md-12">
            <div class="card">
              <div class="card-header">
                <h6 class="mb-0">
                  领涨股：
                  <a href="{% url 'market_data:stock_detail' board.leading_stock %}"> {{ board.leading_stock }} </a>
                  <span
                    class="{% if board.leading_stock_change_percent > 0 %}text-danger{% elif board.leading_stock_change_percent < 0 %}text-success{% endif %}"
                  >
                    {{ board.leading_stock_change_percent|floatformat:2 }}%
                  </span>
                </h6>
              </div>
              <div class="card-body">
                <p class="text-muted mb-0">交易日期：{{ board.date|date:"Y-m-d" }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 成分股列表 -->
<div class="row">
  <div class="col-md-12">
    <div class="card">
      <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">成分股列表</h5>
        <div class="btn-toolbar">
          <div class="dropdown me-2">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">排序方式</button>
            <ul class="dropdown-menu">
              <li>
                <a class="dropdown-item {% if sort_by == '-change_percent' %}active{% endif %}" href="?date={{ selected_date_str }}&sort=-change_percent">
                  涨跌幅 (降序)
                </a>
              </li>
              <li>
                <a class="dropdown-item {% if sort_by == 'change_percent' %}active{% endif %}" href="?date={{ selected_date_str }}&sort=change_percent">
                  涨跌幅 (升序)
                </a>
              </li>
              <li>
                <a class="dropdown-item {% if sort_by == '-turnover_rate' %}active{% endif %}" href="?date={{ selected_date_str }}&sort=-turnover_rate">
                  换手率 (降序)
                </a>
              </li>
              <li>
                <a class="dropdown-item {% if sort_by == '-amount' %}active{% endif %}" href="?date={{ selected_date_str }}&sort=-amount"> 成交额 (降序) </a>
              </li>
              <li>
                <a class="dropdown-item {% if sort_by == 'rank' %}active{% endif %}" href="?date={{ selected_date_str }}&sort=rank"> 序号 (升序) </a>
              </li>
            </ul>
          </div>
          <span class="badge bg-primary text-white">{{ total_items }} 只股票</span>
        </div>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th scope="col">序号</th>
                <th scope="col">股票代码</th>
                <th scope="col">股票名称</th>
                <th scope="col">最新价</th>
                <th scope="col">涨跌幅</th>
                <th scope="col">涨跌额</th>
                <th scope="col">成交量(万手)</th>
                <th scope="col">成交额(亿元)</th>
                <th scope="col">换手率</th>
                <th scope="col">市盈率</th>
                <th scope="col">市净率</th>
              </tr>
            </thead>
            <tbody>
              {% for stock in page_obj %}
              <tr>
                <td>{{ stock.rank|default:forloop.counter }}</td>
                <td>{{ stock.stock_code }}</td>
                <td>
                  <a href="{% url 'market_data:stock_detail' stock.stock_code %}"> {{ stock.stock_name }} </a>
                </td>
                <td>{{ stock.latest_price|floatformat:2 }}</td>
                <td class="{% if stock.change_percent > 0 %}text-danger{% elif stock.change_percent < 0 %}text-success{% endif %}">
                  {{ stock.change_percent|floatformat:2 }}%
                </td>
                <td class="{% if stock.change_amount > 0 %}text-danger{% elif stock.change_amount < 0 %}text-success{% endif %}">
                  {{ stock.change_amount|floatformat:2 }}
                </td>
                <td>{{ stock.volume|floatformat:2 }}</td>
                <td>{{ stock.amount|floatformat:2 }}</td>
                <td>{{ stock.turnover_rate|floatformat:2 }}%</td>
                <td>{{ stock.pe_ratio|floatformat:2 }}</td>
                <td>{{ stock.pb_ratio|floatformat:2 }}</td>
              </tr>
              {% empty %}
              <tr>
                <td colspan="11" class="text-center">暂无成分股数据</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-3">
          <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
              <a class="page-link" href="?date={{ selected_date_str }}&page=1{% if sort_by %}&sort={{ sort_by }}{% endif %}" aria-label="First">
                <span aria-hidden="true">&laquo;&laquo;</span>
              </a>
            </li>
            <li class="page-item">
              <a
                class="page-link"
                href="?date={{ selected_date_str }}&page={{ page_obj.previous_page_number }}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
                aria-label="Previous"
              >
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            {% else %}
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="First">
                <span aria-hidden="true">&laquo;&laquo;</span>
              </a>
            </li>
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
              </a>
            </li>
            {% endif %} {% for num in page_obj.paginator.page_range %} {% if page_obj.number == num %}
            <li class="page-item active">
              <a class="page-link" href="#">{{ num }}</a>
            </li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
            <li class="page-item">
              <a class="page-link" href="?date={{ selected_date_str }}&page={{ num }}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ num }}</a>
            </li>
            {% endif %} {% endfor %} {% if page_obj.has_next %}
            <li class="page-item">
              <a
                class="page-link"
                href="?date={{ selected_date_str }}&page={{ page_obj.next_page_number }}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
                aria-label="Next"
              >
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
            <li class="page-item">
              <a
                class="page-link"
                href="?date={{ selected_date_str }}&page={{ page_obj.paginator.num_pages }}{% if sort_by %}&sort={{ sort_by }}{% endif %}"
                aria-label="Last"
              >
                <span aria-hidden="true">&raquo;&raquo;</span>
              </a>
            </li>
            {% else %}
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
              </a>
            </li>
            <li class="page-item disabled">
              <a class="page-link" href="#" aria-label="Last">
                <span aria-hidden="true">&raquo;&raquo;</span>
              </a>
            </li>
            {% endif %}
          </ul>
        </nav>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- 数据说明 -->
<div class="card mt-4 mb-4">
  <div class="card-header">
    <h5 class="mb-0">数据说明</h5>
  </div>
  <div class="card-body">
    <p><strong>行业板块成分股</strong>：表示属于同一行业分类的股票集合，反映该行业的整体表现和内部结构。</p>
    <p><strong>数据字段说明</strong>：</p>
    <ul>
      <li><strong>最新价</strong>：股票当前交易价格</li>
      <li><strong>涨跌幅/涨跌额</strong>：相比上一交易日的变化</li>
      <li><strong>成交量/成交额</strong>：当日的交易量和交易金额</li>
      <li><strong>换手率</strong>：成交股数与流通股本的比值，表示股票交易活跃度</li>
      <li><strong>市盈率</strong>：股票价格与每股收益的比值，表示投资回报周期</li>
      <li><strong>市净率</strong>：股票价格与每股净资产的比值，表示股票价值相对于账面价值的溢价</li>
    </ul>
    <p class="text-muted small mt-2">数据更新时间：每个交易日收盘后</p>
  </div>
</div>

{% endif %} {% endblock %} {% block extra_css %}
<style>
  .text-danger {
    color: #d63939 !important;
  }

  .text-success {
    color: #2fb344 !important;
  }

  /* 确保所有 badge 的文字颜色正确 */
  .badge.bg-primary {
    color: #ffffff !important;
  }

  .badge.bg-secondary {
    color: #ffffff !important;
  }

  .badge.bg-success {
    color: #ffffff !important;
  }

  .badge.bg-danger {
    color: #ffffff !important;
  }

  .badge.bg-warning {
    color: #000000 !important;
  }

  .badge.bg-info {
    color: #ffffff !important;
  }
</style>
{% endblock %}
